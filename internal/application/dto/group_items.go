package dto

import (
	"encoding/json"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// SLS Grouping Items API DTO
// ============================================================================
//
// 架构设计：SLS端专注纯库存寻源，MP端处理所有业务逻辑
// - MP端：业务决策逻辑转换为抽象Rules传递给SLS
// - SLS端：执行Rules进行库存分配和分组，返回寻源结果
//
// 核心改变：
// - 删除业务逻辑控制开关（SourcingOptions）
// - 所有业务逻辑（Choice、直配、卖家物流等）抽象为Rules
// - SLS成为纯"库存寻源引擎"而非"业务规则处理器"
// ============================================================================

// 请求相关结构体
// ============================================================================

// GroupItemsRequest 库存寻源API请求结构体
// SLS端专注纯库存寻源，所有业务逻辑由MP端转换成Rules传递
type GroupItemsRequest struct {
	BuyerUserId  uint64       `json:"buyer_user_id" validate:"required"`
	BuyerAddress BuyerAddress `json:"buyer_address"`

	// 要进行库存寻源的商品清单
	Items []*SourcingItem `json:"items" validate:"required,min=1,dive,required"`

	// MP端业务逻辑转换的抽象规则
	GroupingRules *GroupingRules `json:"grouping_rules"`

	// 业务特性开关
	FeatureOption *FeatureOption `json:"feature_option"`
}

// Validate 验证请求参数
func (r *GroupItemsRequest) Validate() fsserr.Error {
	// 1. 业务逻辑验证：检查QueryId唯一性
	queryIds := make(map[string]bool)
	for _, item := range r.Items {
		if queryIds[item.QueryId] {
			return fsserr.New(fsserr.ParamErr, "duplicate query_id: %s", item.QueryId)
		}
		queryIds[item.QueryId] = true
	}

	// 2. 验证规则引用的QueryId都存在
	if r.GroupingRules != nil {
		if err := r.GroupingRules.Validate(queryIds); err != nil {
			return fsserr.New(fsserr.ParamErr, "grouping_rules validation failed: %v", err)
		}
	}

	return nil
}

type BuyerAddress struct {
	State          string `json:"state"`
	City           string `json:"city"`
	District       string `json:"district"`
	BuyerAddressId uint64 `json:"buyer_address_id"`
	Required       bool   `json:"required"`
}

// SourcingItem 商品结构，只保留库存寻源必需字段
type SourcingItem struct {
	QueryId                    string          `json:"query_id" validate:"required"`
	ShopId                     uint64          `json:"shop_id" validate:"required"`
	ItemId                     typ.ItemIdType  `json:"item_id" validate:"required"`
	ModelId                    typ.ModelIdType `json:"model_id" validate:"required"`
	Quantity                   uint32          `json:"quantity" validate:"required,min=1"`
	RequireAdvanceBookingOrder bool            `json:"require_advance_booking_order"`

	// MP查询到的可用库存信息
	FulfilmentTypeToLocations map[constant.FulfilmentType]group_entity.StockLocations `json:"fulfilment_type_to_locations"`
}

// ============================================================================
// 规则相关结构体
// ============================================================================

// GroupingRules MP端业务逻辑转换的抽象规则结构
// 将Choice、DirectDelivery、SellerLogistics等业务逻辑抽象为通用规则
type GroupingRules struct {
	BundleRules     []*BundleRule     `json:"bundle_rules"`     // 捆绑规则 (促销商品、套装等)
	IsolationRules  []*IsolationRule  `json:"isolation_rules"`  // 隔离规则 (Choice、直配、卖家物流等)
	ConstraintRules []*ConstraintRule `json:"constraint_rules"` // 约束规则 (单源、禁止拆分等)
	ChannelRules    []*ChannelRule    `json:"channel_rules"`    // 渠道规则 (平台合并等)
}

// Validate 验证分组规则
func (g *GroupingRules) Validate(validQueryIds map[string]bool) error {
	// 验证捆绑规则
	for idx, rule := range g.BundleRules {
		if err := rule.Validate(validQueryIds); err != nil {
			return fsserr.New(fsserr.ParamErr, "bundle_rules[%d]: %v", idx, err)
		}
	}

	// 验证隔离规则
	for idx, rule := range g.IsolationRules {
		if err := rule.Validate(validQueryIds); err != nil {
			return fsserr.New(fsserr.ParamErr, "isolation_rules[%d]: %v", idx, err)
		}
	}

	// 验证约束规则
	for idx, rule := range g.ConstraintRules {
		if err := rule.Validate(validQueryIds); err != nil {
			return fsserr.New(fsserr.ParamErr, "constraint_rules[%d]: %v", idx, err)
		}
	}

	// 验证渠道规则
	for idx, rule := range g.ChannelRules {
		if err := rule.Validate(validQueryIds); err != nil {
			return fsserr.New(fsserr.ParamErr, "channel_rules[%d]: %v", idx, err)
		}
	}

	return nil
}

// BundleRule 捆绑规则：指定商品必须在同一包裹
// 来源：促销套装、礼品包装、BOM商品等MP业务逻辑
type BundleRule struct {
	RuleId       string   `json:"rule_id"`
	ItemQueryIds []string `json:"item_query_ids"`
	Mandatory    bool     `json:"mandatory"` // 强制性规则，失败时报错
}

// Validate 验证捆绑规则
func (b *BundleRule) Validate(validQueryIds map[string]bool) error {
	if b.RuleId == "" {
		return fsserr.New(fsserr.ParamErr, "rule_id is required")
	}
	if len(b.ItemQueryIds) < 2 {
		return fsserr.New(fsserr.ParamErr, "bundle rule requires at least 2 items")
	}

	for _, queryId := range b.ItemQueryIds {
		if !validQueryIds[queryId] {
			return fsserr.New(fsserr.ParamErr, "rule references non-existent query_id: %s", queryId)
		}
	}

	return nil
}

// IsolationRule 隔离规则：符合条件的商品单独分组
// 来源：Shopee Choice、直接配送、卖家物流、以旧换新等MP业务逻辑
type IsolationRule struct {
	RuleId        string           `json:"rule_id"`
	ItemQueryIds  []string         `json:"item_query_ids"`
	Conditions    []*RuleCondition `json:"conditions"`
	IsolationType IsolationType    `json:"isolation_type"`
	Mandatory     bool             `json:"mandatory"` // 是否为强制性规则，失败时报错
}

// Validate 验证隔离规则
func (i *IsolationRule) Validate(validQueryIds map[string]bool) error {
	if i.RuleId == "" {
		return fsserr.New(fsserr.ParamErr, "rule_id is required")
	}
	if len(i.ItemQueryIds) == 0 {
		return fsserr.New(fsserr.ParamErr, "item_query_ids is required")
	}

	// 验证隔离类型
	if i.IsolationType != IsolationTypeSingleItem && i.IsolationType != IsolationTypeIsolatedGroup {
		return fsserr.New(fsserr.ParamErr, "invalid isolation_type: %s", i.IsolationType)
	}

	for _, queryId := range i.ItemQueryIds {
		if !validQueryIds[queryId] {
			return fsserr.New(fsserr.ParamErr, "rule references non-existent query_id: %s", queryId)
		}
	}

	// 验证条件
	for idx, condition := range i.Conditions {
		if err := condition.Validate(); err != nil {
			return fsserr.New(fsserr.ParamErr, "conditions[%d]: %v", idx, err)
		}
	}

	return nil
}

// ConstraintRule 约束规则：商品级别的约束
// 来源：单源要求、禁止拆分等MP业务逻辑
type ConstraintRule struct {
	RuleId         string         `json:"rule_id"`
	ItemQueryIds   []string       `json:"item_query_ids"`
	ConstraintType ConstraintType `json:"constraint_type"`
	Mandatory      bool           `json:"mandatory"` // 是否为强制性规则，失败时报错
}

// Validate 验证约束规则
func (c *ConstraintRule) Validate(validQueryIds map[string]bool) error {
	if c.RuleId == "" {
		return fsserr.New(fsserr.ParamErr, "rule_id is required")
	}
	if len(c.ItemQueryIds) == 0 {
		return fsserr.New(fsserr.ParamErr, "item_query_ids is required")
	}

	// 验证约束类型
	if c.ConstraintType != ConstraintTypeDisableQuantitySplit && c.ConstraintType != ConstraintTypeSingleSourceOnly {
		return fsserr.New(fsserr.ParamErr, "invalid constraint_type: %s", c.ConstraintType)
	}

	for _, queryId := range c.ItemQueryIds {
		if !validQueryIds[queryId] {
			return fsserr.New(fsserr.ParamErr, "rule references non-existent query_id: %s", queryId)
		}
	}

	return nil
}

// ChannelRule 渠道规则：基于物流渠道的规则
// 来源：平台合并、特定渠道限制等MP业务逻辑
type ChannelRule struct {
	RuleId       string           `json:"rule_id"`
	ItemQueryIds []string         `json:"item_query_ids"`
	Conditions   []*RuleCondition `json:"conditions"`
	RuleType     ChannelRuleType  `json:"rule_type"`
	Mandatory    bool             `json:"mandatory"` // 是否为强制性规则，失败时报错
}

// Validate 验证渠道规则
func (c *ChannelRule) Validate(validQueryIds map[string]bool) error {
	if c.RuleId == "" {
		return fsserr.New(fsserr.ParamErr, "rule_id is required")
	}
	if len(c.ItemQueryIds) == 0 {
		return fsserr.New(fsserr.ParamErr, "item_query_ids is required")
	}

	// 验证规则类型
	if c.RuleType != ChannelRuleTypeCommonChannelRequired && c.RuleType != ChannelRuleTypeChannelExclusive {
		return fsserr.New(fsserr.ParamErr, "invalid rule_type: %s", c.RuleType)
	}

	for _, queryId := range c.ItemQueryIds {
		if !validQueryIds[queryId] {
			return fsserr.New(fsserr.ParamErr, "rule references non-existent query_id: %s", queryId)
		}
	}

	// 验证条件
	for idx, condition := range c.Conditions {
		if err := condition.Validate(); err != nil {
			return fsserr.New(fsserr.ParamErr, "conditions[%d]: %v", idx, err)
		}
	}

	return nil
}

// RuleCondition 规则条件
type RuleCondition struct {
	ConditionType ConditionType `json:"condition_type"`
	ChannelIds    []int64       `json:"channel_ids"`
}

// Validate 验证规则条件
func (r *RuleCondition) Validate() error {
	if r.ConditionType == 0 {
		return fsserr.New(fsserr.ParamErr, "condition_type is required")
	}

	// 验证特定条件类型的参数
	switch r.ConditionType {
	case ConditionTypeChannelEnabled:
		if len(r.ChannelIds) == 0 {
			return fsserr.New(fsserr.ParamErr, "channel_enabled condition requires channel_ids")
		}
	default:
		return fsserr.New(fsserr.ParamErr, "unsupported condition_type: %s", r.ConditionType)
	}

	return nil
}

// ============================================================================
// 响应相关结构体
// ============================================================================

// GroupItemsResponse 简化响应，聚焦寻源结果
type GroupItemsResponse struct {
	ShippingGroups []*ShippingGroup `json:"shipping_groups"`
}

// ShippingGroup 发货分组结果
type ShippingGroup struct {
	Items            []*SourcingResultItem `json:"items"`
	FulfillmentInfos []*FulfillmentInfo    `json:"fulfillment_infos"`
}

// SourcingResultItem 寻源结果商品项
type SourcingResultItem struct {
	QueryId  string          `json:"query_id"`
	ItemId   typ.ItemIdType  `json:"item_id"`
	ModelId  typ.ModelIdType `json:"model_id"`
	Quantity uint32          `json:"quantity"`
}

// StockAllocation 库存分配
type StockAllocation struct {
	SourceId     string `json:"source_id"`     // 库存源ID
	SourceType   string `json:"source_type"`   // shopee_warehouse | seller_stock | cache_stock
	AllocatedQty uint32 `json:"allocated_qty"` // 分配数量
	WarehouseId  string `json:"warehouse_id"`  // 仓库ID
	AddressId    uint64 `json:"address_id"`    // 地址ID
	Priority     int    `json:"priority"`      // 优先级
}

// FulfillmentInfo 履约信息
type FulfillmentInfo struct {
	GroupID                   uint32                                    `json:"group_id"`
	AddressId                 uint64                                    `json:"address_id"`
	Source                    string                                    `json:"source"`
	LocationId                string                                    `json:"location_id"`
	GroupIcon                 string                                    `json:"group_icon"`
	GroupDesc                 string                                    `json:"group_desc"`
	Priority                  uint32                                    `json:"priority"`
	ShipmentGroupID           uint64                                    `json:"shipment_group_id"`
	GroupShipment             bool                                      `json:"group_shipment"`
	IsAdvanceBookingOrder     bool                                      `json:"is_advance_booking_order"`
	OrderFulfilmentType       constant.FulfilmentType                   `json:"order_fulfilment_type"`
	AbleFallbackToSellerStock bool                                      `json:"able_fallback_to_seller_stock"`
	ManagedBySBS              bool                                      `json:"managed_by_sbs"`
	Flag                      order_order_info.Constant_OrderDetailFlag `json:"flag"`
}

// ============================================================================
// 枚举类型定义
// ============================================================================

// IsolationType 隔离类型枚举
type IsolationType int

const (
	IsolationTypeSingleItem    IsolationType = iota + 1 // 单商品隔离
	IsolationTypeIsolatedGroup                          // 分组隔离
)

// String 返回隔离类型的字符串表示
func (t IsolationType) String() string {
	switch t {
	case IsolationTypeSingleItem:
		return "single_item"
	case IsolationTypeIsolatedGroup:
		return "isolated_group"
	default:
		return "unknown"
	}
}

// MarshalJSON 实现JSON序列化 - 序列化为INT
func (t IsolationType) MarshalJSON() ([]byte, error) {
	return json.Marshal(int(t))
}

// UnmarshalJSON 实现JSON反序列化 - 从INT反序列化
func (t *IsolationType) UnmarshalJSON(data []byte) error {
	var i int
	if err := json.Unmarshal(data, &i); err != nil {
		return err
	}

	// 验证枚举值有效性
	switch IsolationType(i) {
	case IsolationTypeSingleItem, IsolationTypeIsolatedGroup:
		*t = IsolationType(i)
		return nil
	default:
		return fmt.Errorf("invalid isolation_type value: %d", i)
	}
}

// ParseIsolationType 从字符串解析隔离类型
func ParseIsolationType(s string) (IsolationType, error) {
	switch s {
	case "single_item":
		return IsolationTypeSingleItem, nil
	case "isolated_group":
		return IsolationTypeIsolatedGroup, nil
	default:
		return 0, fmt.Errorf("invalid isolation_type: %s", s)
	}
}

// ConstraintType 约束类型枚举
type ConstraintType int

const (
	ConstraintTypeDisableQuantitySplit ConstraintType = iota + 1 // 禁止数量拆分
	ConstraintTypeSingleSourceOnly                               // 单一库存源
)

// String 返回约束类型的字符串表示
func (t ConstraintType) String() string {
	switch t {
	case ConstraintTypeDisableQuantitySplit:
		return "disable_quantity_split"
	case ConstraintTypeSingleSourceOnly:
		return "single_source_only"
	default:
		return "unknown"
	}
}

// MarshalJSON 实现JSON序列化 - 序列化为INT
func (t ConstraintType) MarshalJSON() ([]byte, error) {
	return json.Marshal(int(t))
}

// UnmarshalJSON 实现JSON反序列化 - 从INT反序列化
func (t *ConstraintType) UnmarshalJSON(data []byte) error {
	var i int
	if err := json.Unmarshal(data, &i); err != nil {
		return err
	}

	// 验证枚举值有效性
	switch ConstraintType(i) {
	case ConstraintTypeDisableQuantitySplit, ConstraintTypeSingleSourceOnly:
		*t = ConstraintType(i)
		return nil
	default:
		return fmt.Errorf("invalid constraint_type value: %d", i)
	}
}

// ParseConstraintType 从字符串解析约束类型
func ParseConstraintType(s string) (ConstraintType, error) {
	switch s {
	case "disable_quantity_split":
		return ConstraintTypeDisableQuantitySplit, nil
	case "single_source_only":
		return ConstraintTypeSingleSourceOnly, nil
	default:
		return 0, fmt.Errorf("invalid constraint_type: %s", s)
	}
}

// ChannelRuleType 渠道规则类型枚举
type ChannelRuleType int

const (
	ChannelRuleTypeCommonChannelRequired ChannelRuleType = iota + 1 // 需要公共渠道
	ChannelRuleTypeChannelExclusive                                 // 渠道排他
)

// String 返回渠道规则类型的字符串表示
func (t ChannelRuleType) String() string {
	switch t {
	case ChannelRuleTypeCommonChannelRequired:
		return "common_channel_required"
	case ChannelRuleTypeChannelExclusive:
		return "channel_exclusive"
	default:
		return "unknown"
	}
}

// MarshalJSON 实现JSON序列化 - 序列化为INT
func (t ChannelRuleType) MarshalJSON() ([]byte, error) {
	return json.Marshal(int(t))
}

// UnmarshalJSON 实现JSON反序列化 - 从INT反序列化
func (t *ChannelRuleType) UnmarshalJSON(data []byte) error {
	var i int
	if err := json.Unmarshal(data, &i); err != nil {
		return err
	}

	// 验证枚举值有效性
	switch ChannelRuleType(i) {
	case ChannelRuleTypeCommonChannelRequired, ChannelRuleTypeChannelExclusive:
		*t = ChannelRuleType(i)
		return nil
	default:
		return fmt.Errorf("invalid channel_rule_type value: %d", i)
	}
}

// ParseChannelRuleType 从字符串解析渠道规则类型
func ParseChannelRuleType(s string) (ChannelRuleType, error) {
	switch s {
	case "common_channel_required":
		return ChannelRuleTypeCommonChannelRequired, nil
	case "channel_exclusive":
		return ChannelRuleTypeChannelExclusive, nil
	default:
		return 0, fmt.Errorf("invalid channel_rule_type: %s", s)
	}
}

// ConditionType 条件类型枚举
type ConditionType int

const (
	ConditionTypeChannelEnabled ConditionType = iota + 1 // 渠道启用条件
)

// String 返回条件类型的字符串表示
func (t ConditionType) String() string {
	switch t {
	case ConditionTypeChannelEnabled:
		return "channel_enabled"
	default:
		return "unknown"
	}
}

// MarshalJSON 实现JSON序列化 - 序列化为INT
func (t ConditionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(int(t))
}

// UnmarshalJSON 实现JSON反序列化 - 从INT反序列化
func (t *ConditionType) UnmarshalJSON(data []byte) error {
	var i int
	if err := json.Unmarshal(data, &i); err != nil {
		return err
	}

	// 验证枚举值有效性
	switch ConditionType(i) {
	case ConditionTypeChannelEnabled:
		*t = ConditionType(i)
		return nil
	default:
		return fmt.Errorf("invalid condition_type value: %d", i)
	}
}

// ParseConditionType 从字符串解析条件类型
func ParseConditionType(s string) (ConditionType, error) {
	switch s {
	case "channel_enabled":
		return ConditionTypeChannelEnabled, nil
	default:
		return 0, fmt.Errorf("invalid condition_type: %s", s)
	}
}

// ============================================================================
// 常量定义
// ============================================================================

// 库存源类型常量
const (
	SourceTypeShopeeWarehouse = "shopee_warehouse"
	SourceTypeSellerStock     = "seller_stock"
	SourceTypeCacheStock      = "cache_stock"
	SourceType3PFStock        = "3pf_stock"
)

type FeatureOption struct {
	SkipSplitByWeightDimension bool `json:"skip_split_by_weight_dimension"` // 是否跳过超材拆单
	SingleSourceOnly           bool `json:"single_source_only"`             // 是否单仓履约，是则同一SKU只能从一个仓库发货
	IsGroupBuy                 bool `json:"is_group_buy"`                   // 是否拼团订单，是则走Group Buy库存/定价逻辑
}
