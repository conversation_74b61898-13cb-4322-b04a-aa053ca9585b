package dto

import (
	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// ConvertPBToGroupItemsRequest converts PB message to DTO
func ConvertPBToGroupItemsRequest(pbReq *fss_proto.GroupItemsRequest) (*GroupItemsRequest, fsserr.Error) {
	if pbReq == nil {
		return nil, fsserr.New(fsserr.ParamErr, "pb request is nil")
	}

	req := &GroupItemsRequest{
		BuyerUserId: pbReq.GetBuyerUserId(),
	}

	// Convert BuyerAddress
	req.BuyerAddress = BuyerAddress{
		State:          pbReq.GetBuyerAddress().GetState(),
		City:           pbReq.GetBuyerAddress().GetCity(),
		District:       pbReq.GetBuyerAddress().GetDistrict(),
		BuyerAddressId: pbReq.GetBuyerAddress().GetBuyerAddressId(),
		Required:       pbReq.GetBuyerAddress().GetRequired(),
	}

	// Convert Items
	req.Items = make([]*SourcingItem, len(pbReq.Items))
	for i, pbItem := range pbReq.GetItems() {
		item := &SourcingItem{
			QueryId:                    pbItem.GetQueryId(),
			ShopId:                     pbItem.GetShopId(),
			ItemId:                     typ.ItemIdType(pbItem.GetItemId()),
			ModelId:                    typ.ModelIdType(pbItem.GetModelId()),
			Quantity:                   pbItem.GetQuantity(),
			RequireAdvanceBookingOrder: pbItem.GetRequireAdvanceBookingOrder(),
		}

		// Directly convert the flat list of PB locations to a flat list of DTO locations
		dtoLocations := make(group_entity.StockLocations, len(pbItem.GetFulfilmentLocations()))
		for i, pbLoc := range pbItem.GetFulfilmentLocations() {
			dtoLocations[i] = group_entity.ItemStockLocation{
				Source:          pbLoc.GetSource(),
				FulfilmentType:  constant.FulfilmentType(pbLoc.GetFulfilmentType()),
				AvailableStock:  pbLoc.GetAvailableStock(),
				EnabledChannels: typ.ConvertIntegerSlices[int64, int](pbLoc.GetEnabledChannels()),
			}
		}

		// Group the flat list into a map for the DTO
		item.FulfilmentTypeToLocations = make(map[constant.FulfilmentType]group_entity.StockLocations)
		for _, loc := range dtoLocations {
			item.FulfilmentTypeToLocations[loc.FulfilmentType] = append(item.FulfilmentTypeToLocations[loc.FulfilmentType], loc)
		}

		req.Items[i] = item
	}

	// Convert GroupingRules
	req.GroupingRules = &GroupingRules{
		BundleRules:     make([]*BundleRule, 0),
		IsolationRules:  make([]*IsolationRule, 0),
		ConstraintRules: make([]*ConstraintRule, 0),
		ChannelRules:    make([]*ChannelRule, 0),
	}

	// ---------------- Bundle Rules ----------------
	for _, pbRule := range pbReq.GetGroupingRules().GetBundleRules() {
		bundleRule := &BundleRule{
			RuleId:       pbRule.GetRuleId(),
			ItemQueryIds: pbRule.GetItemQueryIds(),
			Mandatory:    pbRule.GetMandatory(),
		}
		req.GroupingRules.BundleRules = append(req.GroupingRules.BundleRules, bundleRule)
	}

	// ---------------- Isolation Rules ----------------
	for _, pbRule := range pbReq.GetGroupingRules().GetIsolationRules() {
		isolationRule := &IsolationRule{
			RuleId:        pbRule.GetRuleId(),
			ItemQueryIds:  pbRule.GetItemQueryIds(),
			IsolationType: IsolationType(pbRule.GetIsolationType()),
			Mandatory:     pbRule.GetMandatory(),
		}

		// Convert conditions
		for _, pbCond := range pbRule.GetConditions() {
			cond := &RuleCondition{
				ConditionType: ConditionType(pbCond.GetConditionType()),
				ChannelIds:    typ.ConvertUint64ToInt64(pbCond.GetChannelIds()),
			}
			isolationRule.Conditions = append(isolationRule.Conditions, cond)
		}

		req.GroupingRules.IsolationRules = append(req.GroupingRules.IsolationRules, isolationRule)
	}

	// ---------------- Constraint Rules ----------------
	for _, pbRule := range pbReq.GetGroupingRules().GetConstraintRules() {
		constraintRule := &ConstraintRule{
			RuleId:         pbRule.GetRuleId(),
			ItemQueryIds:   pbRule.GetItemQueryIds(),
			ConstraintType: ConstraintType(pbRule.GetConstraintType()),
			Mandatory:      pbRule.GetMandatory(),
		}
		req.GroupingRules.ConstraintRules = append(req.GroupingRules.ConstraintRules, constraintRule)
	}

	// ---------------- Channel Rules ----------------
	for _, pbRule := range pbReq.GetGroupingRules().GetChannelRules() {
		channelRule := &ChannelRule{
			RuleId:       pbRule.GetRuleId(),
			ItemQueryIds: pbRule.GetItemQueryIds(),
			RuleType:     ChannelRuleType(pbRule.GetRuleType()),
			Mandatory:    pbRule.GetMandatory(),
		}

		for _, pbCond := range pbRule.GetConditions() {
			cond := &RuleCondition{
				ConditionType: ConditionType(pbCond.GetConditionType()),
				ChannelIds:    typ.ConvertUint64ToInt64(pbCond.GetChannelIds()),
			}
			channelRule.Conditions = append(channelRule.Conditions, cond)
		}
		req.GroupingRules.ChannelRules = append(req.GroupingRules.ChannelRules, channelRule)
	}

	// Convert FeatureOption
	req.FeatureOption = &FeatureOption{
		IsGroupBuy:                 pbReq.GetFeatureOption().GetIsGroupBuy(),
		SingleSourceOnly:           pbReq.GetFeatureOption().GetSingleSourceOnly(),
		SkipSplitByWeightDimension: pbReq.GetFeatureOption().GetSkipSplitByWeightDimension(),
		// Note: FeatureFlags field doesn't exist in DTO FeatureOption, so we skip it
	}

	return req, nil
}

// ConvertGroupItemsResponseToPB converts DTO response to PB message
func ConvertGroupItemsResponseToPB(resp *GroupItemsResponse) (*fss_proto.GroupItemsResponse, fsserr.Error) {
	if resp == nil {
		return nil, fsserr.New(fsserr.ParamErr, "response is nil")
	}

	pbResp := &fss_proto.GroupItemsResponse{}
	pbResp.ShippingGroups = make([]*fss_proto.ShippingGroup, len(resp.ShippingGroups))

	for i, group := range resp.ShippingGroups {
		if group == nil {
			continue
		}

		pbGroup := &fss_proto.ShippingGroup{}

		// Convert Items
		pbGroup.Items = make([]*fss_proto.SourcingResultItem, len(group.Items))
		for j, item := range group.Items {
			if item != nil {
				pbGroup.Items[j] = &fss_proto.SourcingResultItem{
					QueryId:  item.QueryId,
					ItemId:   uint64(item.ItemId),
					ModelId:  uint64(item.ModelId),
					Quantity: item.Quantity,
				}
			}
		}

		// Convert FulfillmentInfos
		pbGroup.FulfillmentInfos = make([]*fss_proto.FulfillmentInfo, len(group.FulfillmentInfos))
		for j, info := range group.FulfillmentInfos {
			if info == nil {
				continue
			}

			pbInfo := &fss_proto.FulfillmentInfo{
				GroupId:                   info.GroupID,
				AddressId:                 info.AddressId,
				Source:                    info.Source,
				LocationId:                info.LocationId,
				GroupIcon:                 info.GroupIcon,
				GroupDesc:                 info.GroupDesc,
				Priority:                  info.Priority,
				ShipmentGroupId:           info.ShipmentGroupID,
				GroupShipment:             info.GroupShipment,
				IsAdvanceBookingOrder:     info.IsAdvanceBookingOrder,
				OrderFulfilmentType:       int32(info.OrderFulfilmentType),
				AbleFallbackToSellerStock: info.AbleFallbackToSellerStock,
				ManagedBySbs:              info.ManagedBySBS,
				Flag:                      uint32(info.Flag),
			}

			pbGroup.FulfillmentInfos[j] = pbInfo
		}

		pbResp.ShippingGroups[i] = pbGroup
	}

	return pbResp, nil
}

// ConvertPBToBatchGetSellerWhPriorityRequest converts PB message to DTO
func ConvertPBToBatchGetSellerWhPriorityRequest(pbReq *fss_proto.BatchGetSellerWhPriorityRequest) (*BatchGetSellerWhPriorityRequest, fsserr.Error) {
	if pbReq == nil {
		return nil, fsserr.New(fsserr.ParamErr, "pb request is nil")
	}

	req := &BatchGetSellerWhPriorityRequest{
		ShopIds:        pbReq.ShopIds,
		BuyerUserId:    typ.UserIdType(pbReq.BuyerUserId),
		BuyerAddressId: pbReq.BuyerAddressId,
	}
	return req, nil
}

// ConvertBatchGetSellerWhPriorityResponseToPB converts DTO response to PB message
func ConvertBatchGetSellerWhPriorityResponseToPB(resp *BatchGetSellerWhPriorityResponse) (*fss_proto.BatchGetSellerWhPriorityResponse, fsserr.Error) {
	if resp == nil {
		return nil, fsserr.New(fsserr.ParamErr, "response is nil")
	}

	pbResp := &fss_proto.BatchGetSellerWhPriorityResponse{}
	pbResp.Priorities = make([]*fss_proto.SellerWhPriority, len(resp.Priorities))

	for i, priority := range resp.Priorities {
		if priority == nil {
			continue
		}

		pbPriority := &fss_proto.SellerWhPriority{
			ShopId: priority.ShopId,
		}

		// Convert Locations to WarehousePriorities
		pbPriority.Locations = make([]*fss_proto.SellerWHLocation, len(priority.Locations))
		for j, location := range priority.Locations {
			if location != nil {
				pbPriority.Locations[j] = &fss_proto.SellerWHLocation{
					LocationId: location.LocationId,
					AddressId:  location.AddressId,
				}
			}
		}

		pbResp.Priorities[i] = pbPriority
	}

	return pbResp, nil
}

// ConvertPBToGetEligibleFulfilmentSourceRequest converts PB message to DTO
func ConvertPBToGetEligibleFulfilmentSourceRequest(pbReq *fss_proto.GetEligibleFulfilmentSourceRequest) (*GetEligibleFulfilmentSourceRequest, fsserr.Error) {
	if pbReq == nil {
		return nil, fsserr.New(fsserr.ParamErr, "pb request is nil")
	}

	req := &GetEligibleFulfilmentSourceRequest{
		BuyerUserId: typ.UserIdType(pbReq.BuyerUserId),
	}

	// Convert BuyerAddress
	if pbReq.BuyerAddress != nil {
		req.BuyerAddress = &BuyerAddress{
			State:          pbReq.BuyerAddress.State,
			City:           pbReq.BuyerAddress.City,
			District:       pbReq.BuyerAddress.District,
			BuyerAddressId: pbReq.BuyerAddress.BuyerAddressId,
			Required:       pbReq.BuyerAddress.Required,
		}
	}

	// Convert RequestItemInfos
	req.RequestItemInfos = make([]*RequestItemInfo, len(pbReq.RequestItemInfos))
	for i, pbItemInfo := range pbReq.RequestItemInfos {
		if pbItemInfo == nil {
			continue
		}

		req.RequestItemInfos[i] = &RequestItemInfo{
			ShopId:                   pbItemInfo.ShopId,
			ItemId:                   typ.ItemIdType(pbItemInfo.ItemId),
			ModelId:                  typ.ModelIdType(pbItemInfo.ModelId),
			Quantity:                 pbItemInfo.Quantity,
			FilteredSocId:            pbItemInfo.FilteredSocId,
			OrderStockFulfilmentType: constant.FulfilmentType(pbItemInfo.OrderStockFulfilmentType),
		}
	}

	return req, nil
}

// ConvertGetEligibleFulfilmentSourceResponseToPB converts DTO response to PB message
func ConvertGetEligibleFulfilmentSourceResponseToPB(resp *GetEligibleFulfilmentSourceResponse) (*fss_proto.GetEligibleFulfilmentSourceResponse, fsserr.Error) {
	if resp == nil {
		return nil, fsserr.New(fsserr.ParamErr, "response is nil")
	}

	pbResp := &fss_proto.GetEligibleFulfilmentSourceResponse{}
	pbResp.ResponseItemInfo = make([]*fss_proto.ResponseItemInfo, len(resp.ResponseItemInfo))

	for i, itemInfo := range resp.ResponseItemInfo {
		if itemInfo == nil {
			continue
		}

		pbItemInfo := &fss_proto.ResponseItemInfo{
			ShopId:   itemInfo.ShopId,
			ItemId:   uint64(itemInfo.ItemId),
			ModelId:  uint64(itemInfo.ModelId),
			Quantity: itemInfo.Quantity,
		}

		// Convert EligibleFulfilmentSources
		pbItemInfo.EligibleFulfilmentSources = make([]*fss_proto.EligibleFulfilmentSources, len(itemInfo.EligibleFulfilmentSources))
		for j, source := range itemInfo.EligibleFulfilmentSources {
			pbItemInfo.EligibleFulfilmentSources[j] = &fss_proto.EligibleFulfilmentSources{
				LocationId:               source.Source,
				OrderStockFulfilmentType: uint32(source.FulfilmentType),
			}
		}

		pbResp.ResponseItemInfo[i] = pbItemInfo
	}

	return pbResp, nil
}
