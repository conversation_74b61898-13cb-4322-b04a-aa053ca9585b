package dto

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"
)

func TestConvertPBToGroupItemsRequest(t *testing.T) {
	pbReq := &fss_proto.GroupItemsRequest{
		BuyerUserId: 12345,
		BuyerAddress: &fss_proto.BuyerAddress{
			State:          "SG",
			City:           "Singapore",
			District:       "Central",
			BuyerAddressId: 67890,
			Required:       true,
		},
		Items: []*fss_proto.SourcingItem{
			{
				QueryId:                    "query_1",
				ShopId:                     111,
				ItemId:                     222,
				ModelId:                    333,
				Quantity:                   2,
				RequireAdvanceBookingOrder: false,
				FulfilmentLocations: []*fss_proto.StockLocation{
					{
						Source:          "SG_WH_001",
						AvailableStock:  100,
						FulfilmentType:  1,
						EnabledChannels: []int64{101, 102},
					},
				},
			},
		},
		FeatureOption: &fss_proto.FeatureOption{
			IsGroupBuy:                 false,
			SingleSourceOnly:           true,
			SkipSplitByWeightDimension: false,
		},
	}

	dtoReq, err := ConvertPBToGroupItemsRequest(pbReq)
	require.NoError(t, err)
	require.NotNil(t, dtoReq)

	// Verify basic fields
	assert.Equal(t, uint64(12345), dtoReq.BuyerUserId)
	assert.Equal(t, "SG", dtoReq.BuyerAddress.State)
	assert.Equal(t, "Singapore", dtoReq.BuyerAddress.City)
	assert.Equal(t, "Central", dtoReq.BuyerAddress.District)
	assert.Equal(t, uint64(67890), dtoReq.BuyerAddress.BuyerAddressId)
	assert.True(t, dtoReq.BuyerAddress.Required)

	// Verify items
	require.Len(t, dtoReq.Items, 1)
	item := dtoReq.Items[0]
	assert.Equal(t, "query_1", item.QueryId)
	assert.Equal(t, uint64(111), item.ShopId)
	assert.Equal(t, uint64(222), item.ItemId)
	assert.Equal(t, uint64(333), item.ModelId)
	assert.Equal(t, uint32(2), item.Quantity)
	assert.False(t, item.RequireAdvanceBookingOrder)

	// Verify feature option
	require.NotNil(t, dtoReq.FeatureOption)
	assert.False(t, dtoReq.FeatureOption.IsGroupBuy)
	assert.True(t, dtoReq.FeatureOption.SingleSourceOnly)
	assert.False(t, dtoReq.FeatureOption.SkipSplitByWeightDimension)
}

func TestConvertGroupItemsResponseToPB(t *testing.T) {
	dtoResp := &GroupItemsResponse{
		ShippingGroups: []*ShippingGroup{
			{
				Items: []*SourcingResultItem{
					{
						QueryId:  "query_1",
						ItemId:   222,
						ModelId:  333,
						Quantity: 2,
					},
				},
				FulfillmentInfos: []*FulfillmentInfo{
					{
						Source:                "SG_WH_001",
						AddressId:             1001,
						LocationId:            "SG_WH_001",
						OrderFulfilmentType:   1,
						GroupShipment:         false,
						IsAdvanceBookingOrder: false,
					},
				},
			},
		},
	}

	pbResp, err := ConvertGroupItemsResponseToPB(dtoResp)
	require.NoError(t, err)
	require.NotNil(t, pbResp)

	// Verify shipping groups
	require.Len(t, pbResp.ShippingGroups, 1)
	group := pbResp.ShippingGroups[0]

	// Verify items
	require.Len(t, group.Items, 1)
	item := group.Items[0]
	assert.Equal(t, "query_1", item.QueryId)
	assert.Equal(t, uint64(222), item.ItemId)
	assert.Equal(t, uint64(333), item.ModelId)
	assert.Equal(t, uint32(2), item.Quantity)

	// Verify fulfillment infos
	require.Len(t, group.FulfillmentInfos, 1)
	fulfillment := group.FulfillmentInfos[0]
	assert.Equal(t, "SG_WH_001", fulfillment.Source)
}

func TestConvertPBToBatchGetSellerWhPriorityRequest(t *testing.T) {
	pbReq := &fss_proto.BatchGetSellerWhPriorityRequest{
		ShopIds:        []uint64{111, 222, 333},
		BuyerUserId:    12345,
		BuyerAddressId: 67890,
	}

	dtoReq, err := ConvertPBToBatchGetSellerWhPriorityRequest(pbReq)
	require.NoError(t, err)
	require.NotNil(t, dtoReq)

	assert.Equal(t, []uint64{111, 222, 333}, dtoReq.ShopIds)
	assert.Equal(t, uint64(12345), uint64(dtoReq.BuyerUserId))
	assert.Equal(t, uint64(67890), dtoReq.BuyerAddressId)
}

func TestConvertBatchGetSellerWhPriorityResponseToPB(t *testing.T) {
	dtoResp := &BatchGetSellerWhPriorityResponse{
		Priorities: []*PriorityInfo{
			{
				ShopId: 111,
				Locations: []*SellerWHLocation{
					{
						LocationId: "SG_WH_001",
						AddressId:  1001,
					},
					{
						LocationId: "SG_WH_002",
						AddressId:  1002,
					},
				},
			},
		},
	}

	pbResp, err := ConvertBatchGetSellerWhPriorityResponseToPB(dtoResp)
	require.NoError(t, err)
	require.NotNil(t, pbResp)

	require.Len(t, pbResp.Priorities, 1)
	priority := pbResp.Priorities[0]
	assert.Equal(t, uint64(111), priority.ShopId)

	require.Len(t, priority.Locations, 2)
	wh1 := priority.Locations[0]
	assert.Equal(t, uint64(1001), wh1.AddressId)
	assert.Equal(t, "SG_WH_001", wh1.LocationId)

	wh2 := priority.Locations[1]
	assert.Equal(t, uint64(1002), wh2.AddressId)
	assert.Equal(t, "SG_WH_002", wh2.LocationId)
}
