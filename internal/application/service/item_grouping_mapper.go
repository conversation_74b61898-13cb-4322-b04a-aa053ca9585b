package service

import (
	"context"
	"slices"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_order_info.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/dto"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func (s *ItemGroupingServiceImpl) convertRequestToShippingOrders(ctx context.Context, req *dto.GroupItemsRequest) ([]entity2.ShippingOrder, fsserr.Error) {
	if req == nil || len(req.Items) == 0 {
		return nil, nil
	}

	// 获取 IsGroupBuy 标志，用于传递给库存服务
	isGroupBuy := false
	if req.FeatureOption != nil {
		isGroupBuy = req.FeatureOption.IsGroupBuy
	}

	shippingOrders := make([]entity2.ShippingOrder, 0, len(req.Items))
	for _, item := range req.Items {
		if item == nil {
			continue
		}

		shippingOrder, err := s.buildSingleShippingOrder(ctx, item, req.FeatureOption, typ.UserIdType(req.BuyerUserId), isGroupBuy)
		if err != nil {
			return nil, err
		}

		shippingOrders = append(shippingOrders, shippingOrder)
	}

	return shippingOrders, nil
}

// buildSingleShippingOrder 根据单个 SourcingItem 构建 ShippingOrder
func (s *ItemGroupingServiceImpl) buildSingleShippingOrder(
	ctx context.Context, item *dto.SourcingItem, featureOption *dto.FeatureOption, buyerUserId typ.UserIdType, isGroupBuy bool,
) (entity2.ShippingOrder, fsserr.Error) {

	shopInfoMap, err := s.buildShopInfos(ctx, []uint64{item.ShopId})
	if err != nil {
		return entity2.ShippingOrder{}, fsserr.New(fsserr.DataErr, "failed to build shop infos")
	}

	// buildShopInfos 已保证即使获取详情失败也会回传零值 ShopInfo，因此此处无需额外兜底
	shopInfo := shopInfoMap[item.ShopId]

	// 构建 ShippingOrderItem，参考 fulfilment_planning 的转换逻辑
	shippingOrderItem := entity2.ShippingOrderItem{
		QueryId:                   item.QueryId,
		ShopInfo:                  shopInfo,
		ItemID:                    item.ItemId,
		ModelID:                   item.ModelId,
		Quantity:                  item.Quantity,
		FulfilmentTypeToLocations: item.FulfilmentTypeToLocations,
	}

	// 设置SingleSource标志
	if featureOption != nil {
		shippingOrderItem.SingleSource = featureOption.SingleSourceOnly
	}

	shippingOrder := entity2.ShippingOrder{
		RequireAdvanceBooking: item.RequireAdvanceBookingOrder,
		Items:                 []entity2.ShippingOrderItem{shippingOrderItem},
	}

	// 库存验证逻辑 - 参考 fulfilment_planning 中的库存检查
	// 检查每个item的TotalStock是否满足Quantity要求
	if err := s.validateStockSufficiency(&shippingOrderItem); err != nil {
		return entity2.ShippingOrder{}, err
	}

	return shippingOrder, nil
}

// buildShopInfos 获取店铺信息，参考 fulfilment_planning 中的实现
func (s *ItemGroupingServiceImpl) buildShopInfos(ctx context.Context, shopIDs []uint64) (map[uint64]entity2.ShopInfo, error) {
	futureShopDetails := concurrency.GoFuture(func() (map[uint64]entity.ShopDetailInfo, error) {
		return s.shopService.BatchGetShopDetail(ctx, shopIDs)
	})
	futureShopTags := concurrency.GoFuture(func() (map[uint64]map[string]bool, error) {
		return s.getShopTags(ctx, shopIDs)
	})
	futureWarehouseFlags := concurrency.GoFuture(func() (map[uint64]entity.ShopWarehouseFlag, error) {
		return s.shopService.BatchGetShopWarehouseFlag(ctx, shopIDs)
	})
	futureWarehouses := concurrency.GoFuture(func() (map[uint64][]entity.ShopWarehouse, error) {
		return s.shopService.BatchGetShopWarehouses(ctx, shopIDs)
	})

	if err := concurrency.FutureFirstErrorConcurrent(ctx, futureShopDetails, futureShopTags, futureWarehouseFlags, futureWarehouses); err != nil {
		return nil, err
	}

	// 获取结果
	shopDetails := futureShopDetails.Val(ctx)
	shopTags := futureShopTags.Val(ctx)
	warehouseFlags := futureWarehouseFlags.Val(ctx)
	warehouses := futureWarehouses.Val(ctx)

	// 构建ShopInfo映射
	shopInfoMap := make(map[uint64]entity2.ShopInfo)
	for _, shopID := range shopIDs {
		shopDetail := shopDetails[shopID]
		warehouseFlag := warehouseFlags[shopID]
		tagToAvailable := shopTags[shopID]

		// 构建ShopFlag - 参考fulfillment planning的buildShopFlag逻辑
		shopFlag := s.buildShopFlag(shopDetail, warehouseFlag, tagToAvailable)

		// 计算能力标志（完全匹配 shopFlag）
		shopeeWarehouseFulfilmentCapability := slices.Contains(s.configAccessor.GetIGSShopBusinessModeConfig(ctx).ShopeeWarehouseFulfilmentCapabilityShopFlags, shopFlag)
		sellerStockFulfilmentCapability := slices.Contains(s.configAccessor.GetIGSShopBusinessModeConfig(ctx).SellerStockFulfilmentCapabilityShopFlags, shopFlag)

		// 获取PFF分配标签 - 参考fulfillment planning的getPFFAllocateBySalesOrdersGroupTag
		pffAllocateTag := s.getPFFAllocateBySalesOrdersGroupTag(ctx, tagToAvailable)

		shopInfo := entity2.ShopInfo{
			ShopID:                              shopID,
			SellerID:                            shopDetail.UserID,
			ShopFlag:                            shopFlag,
			SellerWarehouseFlag:                 warehouseFlag,
			Warehouses:                          warehouses[shopID],
			ShopeeWarehouseFulfilmentCapability: shopeeWarehouseFulfilmentCapability,
			SellerStockFulfilmentCapability:     sellerStockFulfilmentCapability,
			AdvanceBookingFulfilmentCapability:  true, // TODO: 根据配置设置，fulfillment planning中也是hardcode true
			PFFCheckoutImprovementWhitelist:     tagToAvailable[constant.ShopTagPFFImprove],
			PFFAllocateBySalesOrdersGroupTag:    pffAllocateTag,
			IsPreSaleVoucherSeller:              tagToAvailable[constant.ShopTagSellerPresaleVoucher],
		}

		shopInfoMap[shopID] = shopInfo
	}

	return shopInfoMap, nil
}

// buildShopFlag 构建店铺标志位，一比一迁移 fulfillment planning 的逻辑
func (s *ItemGroupingServiceImpl) buildShopFlag(
	shopDetail entity.ShopDetailInfo,
	warehouseFlag entity.ShopWarehouseFlag,
	tagToAvailable map[string]bool,
) uint64 {
	var shopFlag uint64

	// 根据店铺标签设置标志位 - 参考fulfillment planning的ShopTagToFlag映射
	for shopTag, flag := range entity2.ShopTagToFlag {
		if tagToAvailable[shopTag] {
			shopFlag |= flag
		}
	}

	// 根据店铺详情设置标志位
	if shopDetail.IsSIPPrimary {
		shopFlag |= entity2.ShopFlagSIPPrimary
	}
	if shopDetail.IsSIPAffiliated {
		shopFlag |= entity2.ShopFlagSIPAffiliated
	}
	if shopDetail.IsShipFromOverseas {
		shopFlag |= entity2.ShopFlagCB
	}

	// 根据仓库标志设置标志位
	if warehouseFlag.IsMultiWarehouse() {
		shopFlag |= entity2.ShopFlagSellerMWH
	}
	if warehouseFlag.Is3PFWhitelist() {
		shopFlag |= entity2.ShopFlag3PF
	}

	return shopFlag
}

// getShopTags 获取店铺标签，参考 fulfillment planning 的实现
func (s *ItemGroupingServiceImpl) getShopTags(ctx context.Context, shopIDs []uint64) (map[uint64]map[string]bool, error) {

	sellerTags := []string{
		constant.ShopTagSBS,
		constant.ShopTagFBS,
		constant.ShopTagLocalRetail,
		constant.ShopTagLocalRetailCB,
	}

	// 根据配置动态添加标签
	if s.configAccessor.GetIGSDynamicConfig(ctx).DowngradeToggle.EnablePFFCheckoutAllocationImprovement {
		sellerTags = append(sellerTags, constant.ShopTagPFFImprove)
	}

	// 获取非关键标签
	nonCriticalTags := s.getNonCriticalShopTags(ctx)
	if len(nonCriticalTags) > 0 {
		sellerTags = append(sellerTags, nonCriticalTags...)
	}

	return s.sellerTagService.BatchGetShopSellerTag(ctx, shopIDs, sellerTags)
}

// getNonCriticalShopTags 获取非关键店铺标签，参考 fulfillment planning 的实现
func (s *ItemGroupingServiceImpl) getNonCriticalShopTags(ctx context.Context) []string {
	var sellerTags []string

	// 根据区域和配置动态添加标签
	if s.configAccessor.GetIsShopeeFoodRegion(ctx) {
		sellerTags = append(sellerTags, constant.ShopTagSellerPresaleVoucher)
	}

	// 添加分配仓库的销售订单组标签
	for _, group := range s.configAccessor.GetAllocateWarehouseBySalesOrderGroups(ctx) {
		sellerTags = append(sellerTags, group.SellerTag)
	}

	return sellerTags
}

// getPFFAllocateBySalesOrdersGroupTag 获取PFF分配标签，参考 fulfillment planning 的实现
func (s *ItemGroupingServiceImpl) getPFFAllocateBySalesOrdersGroupTag(ctx context.Context, tagToAvailable map[string]bool) string {
	for _, group := range s.configAccessor.GetAllocateWarehouseBySalesOrderGroups(ctx) {
		if tagToAvailable[group.SellerTag] {
			return group.SellerTag
		}
	}
	return ""
}

// convertShippingOrdersToResponse 将处理后的ShippingOrder转换为DTO响应
func (s *ItemGroupingServiceImpl) convertShippingOrdersToResponse(ctx context.Context, shippingOrders []entity2.ShippingOrder, buyerUserID typ.UserIdType) *dto.GroupItemsResponse {
	shippingGroups := make([]*dto.ShippingGroup, 0, len(shippingOrders))

	for idx, shippingOrder := range shippingOrders {
		shippingGroup := &dto.ShippingGroup{
			Items:            buildSourcingResultItems(shippingOrder.Items),
			FulfillmentInfos: s.buildFulfillmentInfos(ctx, &shippingOrder, buyerUserID, idx),
		}

		shippingGroups = append(shippingGroups, shippingGroup)
	}

	return &dto.GroupItemsResponse{
		ShippingGroups: shippingGroups,
	}
}

// buildSourcingResultItems 构建SourcingResultItem列表
func buildSourcingResultItems(items []entity2.ShippingOrderItem) []*dto.SourcingResultItem {
	resultItems := make([]*dto.SourcingResultItem, 0, len(items))

	for _, item := range items {
		resultItems = append(resultItems, &dto.SourcingResultItem{
			QueryId:  item.QueryId,
			ItemId:   item.ItemID,
			ModelId:  item.ModelID,
			Quantity: item.Quantity,
		})
	}

	return resultItems
}

// buildFulfillmentInfos 构建履约信息数组，参考 fulfillment planning 的逻辑
// 主要履约信息 + Cache Fallback 履约信息（如果存在）
func (s *ItemGroupingServiceImpl) buildFulfillmentInfos(ctx context.Context, shippingOrder *entity2.ShippingOrder, buyerUserID typ.UserIdType, groupId int) []*dto.FulfillmentInfo {
	var fulfillmentInfos []*dto.FulfillmentInfo

	// 主要履约信息
	mainFulfillmentInfo := s.buildSingleFulfillmentInfo(ctx, shippingOrder, shippingOrder.FulfilmentInfo, buyerUserID, groupId)
	fulfillmentInfos = append(fulfillmentInfos, mainFulfillmentInfo)

	// Cache Fallback 履约信息（如果存在）
	// 参考 fulfillment planning 中的逻辑：如果 CacheFallbackFulfilmentInfo.Exists()，则添加到数组中
	if shippingOrder.CacheFallbackFulfilmentInfo.Exists() {
		fallbackInfo := shippingOrder.CacheFallbackFulfilmentInfo.Get()
		fallbackFulfillmentInfo := s.buildSingleFulfillmentInfo(ctx, shippingOrder, fallbackInfo, buyerUserID, groupId)
		fulfillmentInfos = append(fulfillmentInfos, fallbackFulfillmentInfo)
	}

	return fulfillmentInfos
}

// buildSingleFulfillmentInfo 构建单个履约信息，一比一迁移 fulfillment planning 的 buildFulfilmentInfo 逻辑
func (s *ItemGroupingServiceImpl) buildSingleFulfillmentInfo(
	ctx context.Context,
	shippingOrder *entity2.ShippingOrder,
	fulfilmentInfo entity2.OrderFulfilmentInfo,
	buyerUserID typ.UserIdType,
	groupID int,
) *dto.FulfillmentInfo {
	resFulfilmentInfo := &dto.FulfillmentInfo{
		GroupID:                   uint32(groupID),
		Source:                    s.getShippingOrderFulfilmentSource(shippingOrder, fulfilmentInfo),
		LocationId:                fulfilmentInfo.Source,
		GroupIcon:                 shippingOrder.GroupShipmentInfo.Icon,
		GroupDesc:                 shippingOrder.GroupShipmentInfo.Description,
		Priority:                  uint32(shippingOrder.GroupShipmentInfo.Priority),
		ShipmentGroupID:           uint64(shippingOrder.GroupShipmentInfo.ID),
		GroupShipment:             shippingOrder.IsGroupShipment,
		IsAdvanceBookingOrder:     shippingOrder.IsCacheOrder(),
		OrderFulfilmentType:       fulfilmentInfo.Type,
		AbleFallbackToSellerStock: fulfilmentInfo.AbleToFallbackSellerStock, // deprecate later after cache order phase 2
		AddressId:                 fulfilmentInfo.AddressID,
		ManagedBySBS:              shippingOrder.IsManageBySBS(),
	}

	if shippingOrder.IsGroupShipment {
		resFulfilmentInfo.Flag = order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_SHOPEE
		resFulfilmentInfo.OrderFulfilmentType = constant.FulfilmentTypeSeller
	} else if fulfilmentInfo.Type == constant.FulfilmentTypeCacheSeller {
		resFulfilmentInfo.Flag = order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_LOCAL_SELLER
		resFulfilmentInfo.OrderFulfilmentType = constant.FulfilmentTypeSeller
	} else if fulfilmentInfo.Type == constant.FulfilmentTypeCacheWarehouse {
		resFulfilmentInfo.Flag = order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_SHOPEE
		resFulfilmentInfo.OrderFulfilmentType = constant.FulfilmentTypeCacheWarehouse
	} else {
		if fulfilmentInfo.Type == constant.FulfilmentTypeShopee {
			resFulfilmentInfo.Flag = order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_SHOPEE
			resFulfilmentInfo.OrderFulfilmentType = constant.FulfilmentTypeShopee
		} else {
			if shippingOrder.IsDecoupled {
				resFulfilmentInfo.Flag = order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_LOCAL_SELLER
			} else {
				sellerFulfilmentFlag := s.getSellerFulfilmentFlag(ctx, shippingOrder.ShopInfo(), fulfilmentInfo.Source, buyerUserID)
				resFulfilmentInfo.Flag = sellerFulfilmentFlag
			}
			resFulfilmentInfo.ManagedBySBS = false
			resFulfilmentInfo.OrderFulfilmentType = constant.FulfilmentTypeSeller
		}
	}

	if resFulfilmentInfo.Flag&order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_SHOPEE > 0 {
		if shippingOrder.IsServiceByShopee() {
			resFulfilmentInfo.Flag |= order_order_info.Constant_ORDER_DETAIL_FLAG_SERVICE_BY_SHOPEE
		}
	}
	if strings.EqualFold(s.getRegionFromLocationId(resFulfilmentInfo.Source), envvar.GetCIDLower(ctx)) {
		resFulfilmentInfo.Flag |= order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_LOCAL
	}

	return resFulfilmentInfo
}

// getSellerFulfilmentFlag 一比一迁移 fulfillment planning 的 getSellerFulfilmentFlag 逻辑
func (s *ItemGroupingServiceImpl) getSellerFulfilmentFlag(ctx context.Context, shopInfo entity2.ShopInfo, source string, userID typ.UserIdType) order_order_info.Constant_OrderDetailFlag {
	// 3PF 逻辑保持不变
	if shopInfo.SellerWarehouseFlag.Is3PFWhitelist() {
		shipFromRegion := s.getRegionFromLocationId(source)
		if strings.EqualFold(shipFromRegion, envvar.GetCIDLower(ctx)) {
			return order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_LOCAL_SELLER
		}
		return order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_CB_SELLER
	}

	dynCfg := s.configAccessor.GetIGSDynamicConfig(ctx)
	enableShipFromOversea := dynCfg.ShipFromOverseaFlagToggle
	if !enableShipFromOversea {
		for _, id := range dynCfg.ShipFromOverseaFlagUserWhitelist {
			if id == uint64(userID) {
				enableShipFromOversea = true
				break
			}
		}
	}

	if enableShipFromOversea && shopInfo.IsShipFromOversea() {
		return order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_CB_SELLER
	}

	return order_order_info.Constant_ORDER_DETAIL_FLAG_FULFILLED_BY_LOCAL_SELLER
}

// getShippingOrderFulfilmentSource 一比一迁移 fulfillment planning 的 getShippingOrderFulfilmentSource 逻辑
func (s *ItemGroupingServiceImpl) getShippingOrderFulfilmentSource(shippingOrder *entity2.ShippingOrder, fulfilmentInfo entity2.OrderFulfilmentInfo) string {
	if shippingOrder.IsGroupShipment ||
		fulfilmentInfo.Type == constant.FulfilmentTypeShopee || fulfilmentInfo.Type == constant.FulfilmentTypeCacheWarehouse ||
		shippingOrder.ShopInfo().SellerWarehouseFlag.IsMultiWarehouse() ||
		shippingOrder.ShopInfo().SellerWarehouseFlag.Is3PFWhitelist() {
		return fulfilmentInfo.Source
	}

	return ""
}

// getRegionFromLocationId 提取 locationId 前两个字符作为 region，一比一迁移 fulfillment planning 的 GetRegionFromLocationId 逻辑
func (s *ItemGroupingServiceImpl) getRegionFromLocationId(locationId string) string {
	if len(locationId) < 2 {
		return ""
	}
	return strings.ToLower(locationId[0:2])
}

// validateStockSufficiency 验证库存充足性，参考 fulfilment_planning 中的库存检查逻辑
// 检查每个item的TotalStock是否满足Quantity要求
func (s *ItemGroupingServiceImpl) validateStockSufficiency(item *entity2.ShippingOrderItem) fsserr.Error {
	if item == nil {
		return nil
	}

	// 计算总库存
	totalStock := item.TotalStock()

	// 检查总库存是否满足需求量
	if totalStock < item.Quantity {
		// 返回统一的库存不足错误
		return fsserr.New(fsserr.OutOfStockErr,
			"out of stock: itemId=%d, modelId=%d, available=%d, required=%d, shopId=%d",
			item.ItemID, item.ModelID, totalStock, item.Quantity, item.GetShopInfo().ShopID)
	}

	return nil
}
