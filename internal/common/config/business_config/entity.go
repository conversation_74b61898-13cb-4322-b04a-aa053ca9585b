package business_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	abconfig "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"
	"github.com/bytedance/sonic"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

var (
	migrationConfig                                      = utils.NewRecordConfig[MigrationConfig](MigrationKey)
	igs3PFShopFlagsConfig                                = utils.NewJsonRecordConfig[[]uint64](IGS3PFShopFlagsKey)
	igsLFFShopFlags                                      = utils.NewJsonRecordConfig[[]uint64](IGSLFFShopFlagsKey, utils.WithDefaultValue([]uint64{}))
	igsResellModelShopFlags                              = utils.NewJsonRecordConfig[[]uint64](IGSResellModelShopFlagsKey)
	lffWarehouseRegionPriority                           = utils.NewJsonRecordConfig[[]string](LFFWarehouseRegionPriorityKey, utils.WithDefaultValue([]string{}))
	igsDynamicConfig                                     = utils.NewRecordConfig[IGSDynamicConfig](IGSDynamicConfigKey, utils.WithRefreshFunc(igsDynamicConfigRefreshFuncAdapter))
	igsShopBusinessModeConfig                            = utils.NewRecordConfig[IGSShopBusinessModeConfig](IGSShopBusinessModeConfigKey, utils.WithRefreshFunc(igsShopBusinessModeConfigRefreshFuncAdapter))
	isShopeeFoodRegionConfig                             = utils.NewBoolRecordConfig[bool](IsShopeeFoodRegionKey)
	enableWarehouseRegionForAddressAPIConfig             = utils.NewBoolRecordConfig[bool](EnableWarehouseRegionForAddressAPIKey)
	defaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig = utils.NewJsonRecordConfig[map[string]GeoLocationConfig](DefaultSIPPrimaryRegionToDummyBuyerGeoLocationKey)
	defaultBuyerGeoLocationConfig                        = utils.NewRecordConfig[GeoLocationConfig](DefaultBuyerGeoLocationKey)
	abTestingConfig                                      = utils.NewJsonRecordConfig[abconfig.Config](AbTestingKey)
	enableCachedSellerTagFlowConfig                      = utils.NewBoolRecordConfig[bool](EnableCachedSellerTagFlowKey)
	batchGetEntityTagAPISizeConfig                       = utils.NewIntRecordConfig[int](BatchGetEntityTagAPISizeKey, utils.WithDefaultValue(20))
	allocateWarehouseBySalesOrdersGroupConfig            = utils.NewRecordConfig[[]AllocateWarehouseBySalesOrdersGroup](AllocateWarehouseBySalesOrderGroupsKey)
	supportSellerMultiWhPFFConfig                        = utils.NewBoolRecordConfig[bool](SupportSellerMultiWhPFFKey)
	sellerMultiWhWithPartialFBSConfig                    = utils.NewRecordConfig[SellerMultiWhWithPartialFBSConfig](SellerMultiWHWithPartialFBSKey, utils.WithRefreshFunc(sellerMultiWhWithPartialFBSRefreshFuncAdapter))
	enableAllocateWarehouseBySalesOrders                 = utils.NewBoolRecordConfig[bool](EnableAllocateWarehouseBySalesOrdersKey)
	enable3PFIgnoreSellerTagConfig                       = utils.NewBoolRecordConfig[bool](Enable3PFIgnoreSellerTagKey)
	enableIGSPFFRatioAndMultiSellerWHAllocationFixConfig = utils.NewBoolRecordConfig[bool](EnableIGSPFFRatioAndMultiSellerWHAllocationFixKey)
	shopEnableChannelsAPIBatchSizeConfig                 = utils.NewIntRecordConfig[int](BatchGetShopDisplayLogisticChannelsApiSizeKey, utils.WithDefaultValue(20))
	getProductInfoAPISizeConfig                          = utils.NewIntRecordConfig[int](GetProductInfoApiSizeKey, utils.WithDefaultValue(20))
	groupSellerCoverShippingFeeConfig                    = utils.NewIntRecordConfig[int](GroupSellerCoverShippingFeeConfig)
	filterChannelsForPreorderConfig                      = utils.NewJsonRecordConfig[[]int](FilterChannelsForPreorder)
	counterfeitItemChannelBlockToggleConfig              = utils.NewBoolRecordConfig[bool](CounterfeitItemChannelBlockToggleKey)
	counterfeitItemLabelIdConfig                         = utils.NewIntRecordConfig[int](CounterfeitItemLabelIdKey)
	channelsWithSelectableDeliveryTimeConfig             = utils.NewJsonRecordConfig[[]int](ChannelsWithSelectableDeliveryTimeKey)
	weightDimMaxNumOfShippingOrdersConfig                = utils.NewIntRecordConfig[int](WeightDimMaxNumOfShippingOrdersKey)
	weightDimOrderSplitPercentageConfig                  = utils.NewIntRecordConfig[int](WeightDimOrderSplitPercentageKey)
	weightDimOrderSplitWhitelistConfig                   = utils.NewJsonRecordConfig[[]uint64](WeightDimOrderSplitWhitelistKey)
)

type (
	MigrationConfig struct {
		CompareConfig map[string]int `yaml:"compare"`
		SwitchConfig  map[string]int `yaml:"switch"`
	}

	// IGSShopBusinessModeConfig 店铺业务模式配置，一比一迁移 fulfillment planning 的定义
	IGSShopBusinessModeConfig struct {
		ShopeeWarehouseFulfilmentCapabilityShopFlags []uint64 `json:"shopee_warehouse_fulfilment_capability_shop_flags" yaml:"shopee_warehouse_fulfilment_capability_shop_flags"`
		SellerStockFulfilmentCapabilityShopFlags     []uint64 `json:"seller_stock_fulfilment_capability_shop_flags" yaml:"seller_stock_fulfilment_capability_shop_flags"`
	}

	SellerMultiWhWithPartialFBSConfig struct {
		ABTestConfig ABTestConfig `json:"ab_test_config" yaml:"ab_test_config"`
	}

	ABTestConfig struct {
		// Input
		SceneKey    string                 `json:"scene_key" yaml:"scene_key"`
		FeatureKeys []string               `json:"feature_keys" yaml:"feature_keys"`
		BizName     string                 `json:"biz_name" yaml:"biz_name"`
		Params      map[string]interface{} `json:"params" yaml:"params"`

		// Result
		ResultKey            string   `json:"result_key,omitempty" yaml:"result_key,omitempty"`
		ResultTypeToCheckFor string   `json:"result_type_to_check_for,omitempty" yaml:"result_type_to_check_for"` // Default (String):"", JSON:"json"
		Value                string   `json:"value,omitempty" yaml:"value"`                                       // To be used when ResultTypeToCheckFor is string
		JsonTrueFields       []string `json:"json_true_fields,omitempty" yaml:"json_true_fields"`                 // To be used when ResultTypeToCheckFor is JSON
		JsonFalseFields      []string `json:"json_false_fields,omitempty" yaml:"json_false_fields"`               // To be used when ResultTypeToCheckFor is JSON
		Default              bool     `json:"default,omitempty" yaml:"default"`
	}

	AllocateWarehouseBySalesOrdersGroup struct {
		SellerTag                      string  `json:"seller_tag" yaml:"seller_tag"`
		CalculateFromPastDays          int     `json:"calculate_from_past_days" yaml:"calculate_from_past_days"`
		SellerWarehouseSalesOrdersMax  int     `json:"seller_warehouse_sales_orders_max" yaml:"seller_warehouse_sales_orders_max"`
		SellerWarehouseSalesOrdersMin  int     `json:"seller_warehouse_sales_orders_min" yaml:"seller_warehouse_sales_orders_min"`
		SellerWarehouseSalesOrderRatio float64 `json:"seller_warehouse_sales_order_ratio" yaml:"seller_warehouse_sales_order_ratio"`
	}
)

func igsShopBusinessModeConfigRefreshFuncAdapter(ctx context.Context, key string, defaultValue IGSShopBusinessModeConfig) (value IGSShopBusinessModeConfig, err error) {
	type igdShopBusinessModeConfig struct {
		ShopeeWarehouseFulfilmentCapabilityShopFlags string `json:"shopee_warehouse_fulfilment_capability_shop_flags" yaml:"shopee_warehouse_fulfilment_capability_shop_flags"`
		SellerStockFulfilmentCapabilityShopFlags     string `json:"seller_stock_fulfilment_capability_shop_flags" yaml:"seller_stock_fulfilment_capability_shop_flags"`
	}
	c, err := utils.DefaultRefreshFunc(ctx, key, igdShopBusinessModeConfig{})
	if err != nil {
		return defaultValue, err
	}
	if unmarshalErr := sonic.UnmarshalString(c.ShopeeWarehouseFulfilmentCapabilityShopFlags, &value.ShopeeWarehouseFulfilmentCapabilityShopFlags); unmarshalErr != nil {
		logger.LogErrorf("UnmarshalFromString ShopeeWarehouseFulfilmentCapabilityShopFlags failed|config=%s", c.ShopeeWarehouseFulfilmentCapabilityShopFlags)
		return defaultValue, unmarshalErr
	}
	if unmarshalErr := sonic.UnmarshalString(c.SellerStockFulfilmentCapabilityShopFlags, &value.SellerStockFulfilmentCapabilityShopFlags); unmarshalErr != nil {
		logger.LogErrorf("UnmarshalFromString SellerStockFulfilmentCapabilityShopFlags failed|config=%s", c.SellerStockFulfilmentCapabilityShopFlags)
		return defaultValue, unmarshalErr
	}
	return value, nil
}

func sellerMultiWhWithPartialFBSRefreshFuncAdapter(ctx context.Context, key string, defaultValue SellerMultiWhWithPartialFBSConfig) (value SellerMultiWhWithPartialFBSConfig, err error) {
	type sellerMultiWhWithPartialFBSConfig struct {
		ABTestConfig string `json:"ab_test_config" yaml:"ab_test_config"`
	}
	c, err := utils.DefaultRefreshFunc(ctx, key, sellerMultiWhWithPartialFBSConfig{})
	if err != nil {
		return defaultValue, err
	}
	if unmarshalErr := sonic.UnmarshalString(c.ABTestConfig, &value.ABTestConfig); unmarshalErr != nil {
		logger.LogErrorf("UnmarshalFromString ABTestConfig failed|config=%s", c.ABTestConfig)
		return defaultValue, unmarshalErr
	}
	return value, nil
}
