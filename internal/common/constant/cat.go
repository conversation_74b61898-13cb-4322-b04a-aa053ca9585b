package constant

const (
	StatusSuccess                = "0"
	StatusError                  = "error"
	StatusPanic                  = "panic"
	StatusExpired                = "expired"
	StatusNotExist               = "not-exist"
	StatusEmpty                  = "empty"
	StatusLru                    = "lru"
	StatusNotEnable              = "not-enable"
	StatusMiss                   = "miss"
	StatusLoaderEmpty            = "load-empty"
	StatusHotKey                 = "hot-key"
	StatusNamespaceNotFound      = "namespace-not-found"
	StatusNamespaceGroupNotFound = "namespace-group-not-found"
	StatusUnmarshallFuncNotFound = "unmarshall-func-not-found"
	StatusUnmarshallFuncError    = "unmarshall-func-error"
	StatusSetCacheFailure        = "set-cache-failure"
	StatusConvertError           = "convert-error"
)

// 技术模块
const (
	CatModuleRPC          = "RPC.Client"
	CatModuleURL          = "URL"
	CatModulePanic        = "Panic"
	CatModuleAPI          = "API"
	CatModuleRedis        = "Redis"
	CatModuleKafka        = "Kafka"
	CatModuleCache        = "cache"
	CatModuleLocalCache   = "LocalCache"
	CatModuleSpex         = "SpexService"
	CatModuleLruCache     = "cache"
	CatModuleLayeredCache = "LayeredCache"
)

// 业务模块
const (
	ShippingOrderProcess      = "ShippingOrderProcess"
	ShippingOrderProcessTrace = "ShippingOrderProcessTrace"
)

const (
	SalesOrderVolumeControl        = "SalesOrderVolume"
	MetricMarshalOrderPbError      = "MarshalOrderPbError"
	MetricOrderMsgPayloadIsNil     = "OrderMsgPayloadIsNil"
	MetricCountShopSalesOrderError = "CountShopSalesOrderError"
)
