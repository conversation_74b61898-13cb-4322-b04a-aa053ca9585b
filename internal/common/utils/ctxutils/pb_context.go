package ctxutils

import (
	"context"

	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type pbContextKey struct{}

type PBContextData struct {
	Request   proto.Message
	Response  proto.Message
	Error     fsserr.Error
	Operation string
}

// SetPBRequest 将 protobuf 请求存储到 context 中
func SetPBRequest(ctx context.Context, req proto.Message, operation string) context.Context {
	data := &PBContextData{
		Request:   req,
		Operation: operation,
	}
	return context.WithValue(ctx, pbContextKey{}, data)
}

// SetPBResponse 将 protobuf 响应存储到 context 中
func SetPBResponse(ctx context.Context, resp proto.Message) context.Context {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		data.Response = resp
		return context.WithValue(ctx, pbContextKey{}, data)
	}
	return ctx
}

// SetPBError 将错误信息存储到 context 中
func SetPBError(ctx context.Context, err fsserr.Error) context.Context {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		data.Error = err
		return context.WithValue(ctx, pbContextKey{}, data)
	}
	return ctx
}

// SetPBResponseWithError 将 protobuf 响应和错误信息存储到 context 中
func SetPBResponseWithError(ctx context.Context, resp proto.Message, err fsserr.Error) context.Context {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		data.Response = resp
		data.Error = err
		return context.WithValue(ctx, pbContextKey{}, data)
	}
	return ctx
}

// GetPBContextData 从 context 中获取 protobuf 数据
func GetPBContextData(ctx context.Context) *PBContextData {
	if data, ok := ctx.Value(pbContextKey{}).(*PBContextData); ok {
		return data
	}
	return nil
}

// ClearPBContextData 清除 context 中的 protobuf 数据
func ClearPBContextData(ctx context.Context) context.Context {
	return context.WithValue(ctx, pbContextKey{}, nil)
}
