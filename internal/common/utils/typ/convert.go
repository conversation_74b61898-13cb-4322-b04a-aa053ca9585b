package typ

import (
	"reflect"
	"unsafe"
)

func SnakeCase(s string) string {
	var b []byte
	for i := 0; i < len(s); i++ { // proto identifiers are always ASCII
		c := s[i]
		if isASCIIUpper(c) {
			b = append(b, '_')
			c += 'a' - 'A' // convert to lowercase
		}
		b = append(b, c)
	}
	return string(b)
}

func isASCIIUpper(c byte) bool {
	return 'A' <= c && c <= 'Z'
}

func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func StringToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(&s))
}

func ReceiverZeroValue[V any]() V {
	var zeroValue V

	if reflect.TypeOf(zeroValue).Kind() == reflect.Ptr {
		baseType := reflect.TypeOf(zeroValue).Elem()
		newValue := reflect.New(baseType).Interface()
		return newValue.(V)
	}

	return zeroValue
}

func ConvertIntegerSlices[F Integer, T Integer](input []F) []T {
	if input == nil {
		return make([]T, 0)
	}
	result := make([]T, len(input))
	for idx, item := range input {
		result[idx] = T(item)
	}
	return result
}
