package address

import (
	"errors"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_address.pb"
	location_user_location64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/location_user_location.pb"
	"github.com/bytedance/sonic"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

type ExtRegion struct {
	Region ExtGeoInfo `json:"region"`
}

type ExtGeoInfo struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

func ConvertBuyerAddress(addr *location_user_location64.BuyerAddress) (entity.Address, error) {
	if addr == nil {
		// todo error code
		return entity.Address{}, errors.New("empty_user_location_buyer_address")
	}

	var extRegion ExtRegion
	var lat float64
	var lon float64
	err := sonic.Unmarshal([]byte(addr.GetExtinfo().GetGeoinfo()), &extRegion)
	if err == nil {
		lon = extRegion.Region.Longitude
		lat = extRegion.Region.Latitude
	} else {
		lon = addr.GetGeolocation().GetLongitude()
		lat = addr.GetGeolocation().GetLatitude()
	}

	return entity.Address{
		AddressID:   uint64(addr.GetId()),
		UserID:      uint64(addr.GetUserid()),
		AddressType: 0,
		Location: entity.Location{
			Country:   addr.GetCountry(),
			State:     addr.GetState(),
			City:      addr.GetCity(),
			District:  addr.GetDistrict(),
			Town:      addr.GetTown(),
			Longitude: lon,
			Latitude:  lat,
		},
		Address:     addr.GetAddress(),
		FullAddress: addr.GetFullAddress(),
		ZipCode:     addr.GetZipcode(),
		Mtime:       uint64(addr.GetMtime()),
		Phone:       addr.GetPhone(),
		Status:      addr.GetStatus(),
		ExtInfo: entity.AddressExtInfo{
			GeoInfo: addr.GetExtinfo().GetGeoinfo(),
		},
	}, nil
}

func ConvertBasicToAddress(addr account_address.BasicAddress) entity.Address {
	return entity.Address{
		AddressID:   uint64(addr.GetAddressId()),
		UserID:      uint64(addr.GetUserid()),
		Address:     addr.GetLocation().GetAddress(),
		FullAddress: addr.GetLocation().GetAddress(),
		ZipCode:     addr.GetLocation().GetZipcode(),
		Phone:       addr.GetContact().GetPhone(),
		Status:      addr.GetStatus(),
		Location: entity.Location{
			Longitude: addr.GetGeolocation().GetLongitude(),
			Latitude:  addr.GetGeolocation().GetLatitude(),
		},
	}
}

type AddressQuery struct {
	UserID      typ.UserIdType `json:"user_id"`
	AddressID   uint64         `json:"address_id"`
	AddressType int            `json:"address_type"`
}

func AddressQueryKey(query AddressQuery) string {
	return common.GenKey("_", strconv.FormatInt(int64(query.UserID), 10), strconv.FormatUint(query.AddressID, 10), strconv.Itoa(query.AddressType))
}
