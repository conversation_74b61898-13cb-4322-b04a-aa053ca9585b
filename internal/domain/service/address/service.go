package address

//go:generate mockgen --build_flags=--mod=mod -source=service.go -destination=service_mock.go -package=address -mock_names=AddrService=MockAddrService

import (
	"context"
	"strings"

	locationuserlocationpb "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/location_user_location.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type AddrService interface {
	GetShopeeWarehouseAddress(ctx context.Context, warehouseCode string) (entity.Address, fsserr.Error)
	GetDummyBuyerGeoLocation(ctx context.Context, dummyBuyerID typ.UserIdType, sipPrimaryRegion string) (entity.GeoLocation, fsserr.Error)
	GetBuyerGeoLocation(ctx context.Context, userID typ.UserIdType, addressID uint64) (entity.GeoLocation, fsserr.Error)
	GetAddressWithGeo(ctx context.Context, userID typ.UserIdType, addressID uint64) (entity.AddressWithGeo, fsserr.Error)
	BatchGetShopWarehouseGeo(ctx context.Context, userID typ.UserIdType, whAddressIDs []uint64) (map[uint64]entity.GeoLocation, fsserr.Error)
}

type AddrServiceImpl struct {
	spexClient   spexlib.SpexClient
	confAccessor config.ConfAccessor
}

func NewAddressService(spexClient spexlib.SpexClient, confAccessor config.ConfAccessor) *AddrServiceImpl {
	return &AddrServiceImpl{
		spexClient:   spexClient,
		confAccessor: confAccessor,
	}
}

func (s *AddrServiceImpl) GetShopeeWarehouseAddress(ctx context.Context, warehouseCode string) (entity.Address, fsserr.Error) {
	whsAddrMap, err := s.BatchShopeeWarehouseAddress(ctx, []string{warehouseCode})
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get_shopee_warehouse_address_err, warehouse_code: %s, err: %v", warehouseCode, err)
		return entity.Address{}, nil
	}
	return whsAddrMap[warehouseCode], nil
}

func (s *AddrServiceImpl) GetDummyBuyerGeoLocation(ctx context.Context, dummyBuyerID typ.UserIdType, sipPrimaryRegion string) (entity.GeoLocation, fsserr.Error) {
	userDetail, err := s.spexClient.GetAccountDetail(ctx, dummyBuyerID, meta.Region(strings.ToUpper(sipPrimaryRegion)))
	if err != nil {
		Logger.CtxLogErrorf(ctx, "getDummyBuyerGeoLocation|GetUserDeliveryAddressID, buyer id: %d, region: %s, err: %v", dummyBuyerID, sipPrimaryRegion, err)
	} else {
		buyerAddressInfo, err := s.GetAddressWithGeo(ctx, dummyBuyerID, uint64(userDetail.GetDeliveryAddressId()))
		if err != nil {
			Logger.CtxLogErrorf(ctx, "getDummyBuyerGeoLocation|GetAddressWithGeo, buyer id: %d, region: %s, err: %v", dummyBuyerID, sipPrimaryRegion, err)
		} else {
			return buyerAddressInfo.GeoLocation, nil
		}
	}
	defaultGeoLocationMap := s.confAccessor.GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx)
	if len(defaultGeoLocationMap) == 0 {
		return entity.GeoLocation{}, fsserr.New(fsserr.ParamErr, "no default SIP dummy buyer geolocation config")
	}
	defaultGeoLocation, ok := defaultGeoLocationMap[sipPrimaryRegion]
	if !ok {
		return entity.GeoLocation{}, fsserr.New(fsserr.ParamErr, "no default SIP dummy buyer geolocation config")
	}

	Logger.CtxLogDebugf(ctx, "getDummyBuyerGeoLocation|use default dummy buyer geo location, buyer id: %d, region: %s, defaultGeoLocation: %s",
		dummyBuyerID, sipPrimaryRegion, Logger.JsonStringForDebugLog(ctx, defaultGeoLocation))
	return entity.GeoLocation{
		Latitude:  defaultGeoLocation.Latitude,
		Longitude: defaultGeoLocation.Longitude,
	}, nil
}

func (s *AddrServiceImpl) GetBuyerGeoLocation(ctx context.Context, userID typ.UserIdType, addressID uint64) (entity.GeoLocation, fsserr.Error) {
	if addressID > 0 {
		buyerAddressWithGeo, err := s.GetAddressWithGeo(ctx, userID, addressID)
		if err != nil {
			Logger.CtxLogErrorf(ctx, "GetBuyerGeoLocation|GetAddressWithGeo, buyer id: %d, addressID: %d, err: %v", userID, addressID, err)
		} else {
			return buyerAddressWithGeo.GeoLocation, nil
		}
	}

	// todo defaultGeoLocationConfig 没有配置默认会返回 (0,0)
	//      但 MP 侧也是相同逻辑，不会报错. 迁移期间先保持一致
	defaultGeoLocationConfig := s.confAccessor.GetDefaultBuyerGeoLocationConfig(ctx)

	//这些报错逻辑先下掉
	//if defaultGeoLocationConfig.Latitude == 0 && defaultGeoLocationConfig.Longitude == 0 {
	//	return entity.GeoLocation{}, fsserr.New(fsserr.ParamErr, "no default buyer geolocation config")
	//}

	Logger.CtxLogDebugf(ctx, "use default buyer geo location, config: %v", defaultGeoLocationConfig)
	return entity.GeoLocation{
		Latitude:  defaultGeoLocationConfig.Latitude,
		Longitude: defaultGeoLocationConfig.Longitude,
	}, nil
}

func (s *AddrServiceImpl) GetAddressWithGeo(ctx context.Context, userID typ.UserIdType, addressID uint64) (entity.AddressWithGeo, fsserr.Error) {
	addr, err := s.spexClient.GetPrivateAddressWithGeocoding(ctx, userID, addressID)
	if err != nil {
		return entity.AddressWithGeo{}, err
	}
	if addr.GetGeocoding().GetLongitude() == 0 && addr.GetGeocoding().GetLatitude() == 0 && addr.GetGeocoding().GetLocationType() == 0 {
		return entity.AddressWithGeo{}, fsserr.New(fsserr.ParamErr, "no geo-location")
	}
	addrWithGeo := entity.AddressWithGeo{
		GeoLocation: entity.GeoLocation{
			Latitude:  addr.GetGeocoding().GetLatitude(),
			Longitude: addr.GetGeocoding().GetLongitude(),
			Precision: uint8(addr.GetGeocoding().GetLocationType()),
		},
		State:    addr.GetBasic().GetLocation().GetState(),
		City:     addr.GetBasic().GetLocation().GetCity(),
		District: addr.GetBasic().GetLocation().GetDistrict(),
	}
	return addrWithGeo, nil
}

func (s *AddrServiceImpl) BatchShopeeWarehouseAddress(ctx context.Context, warehouseCodes []string) (map[string]entity.Address, fsserr.Error) {
	regionSet := collection.NewSet[string]()
	// IsEnableWarehouseRegionForAddressAPI always false
	if s.confAccessor.IsEnableWarehouseRegionForAddressAPI(ctx) {
		for _, warehouseID := range warehouseCodes {
			if len(warehouseID) < 2 {
				continue
			}
			regionSet.Add(strings.ToUpper(warehouseID[:2]))
		}
	} else {
		regionSet.Add(envvar.GetCID(ctx))
	}
	resp, err := s.getShippingAddresses(ctx, 0, []typ.UserIdType{}, true, regionSet.Values())
	if err != nil {
		return nil, err
	}
	whsIDToAddr := s.processRawWHAddressResponse(resp)

	result := make(map[string]entity.Address, len(whsIDToAddr))
	for _, whsId := range warehouseCodes {
		whsAddr := whsIDToAddr[whsId]
		warehouseAddr, convErr := ConvertBuyerAddress(whsAddr)
		if convErr != nil {
			Logger.CtxLogErrorf(ctx, "BatchShopeeWarehouseAddress|convert_address_err, warehouse_codes: %s, err: %v",
				Logger.JsonString(whsId), convErr)
			// todo error code
			return nil, fsserr.With(fsserr.RateLimitErr, convErr)
		}
		warehouseAddr.IsWMS = true
		result[whsId] = warehouseAddr
	}
	return result, nil
}

func (s *AddrServiceImpl) BatchGetShopWarehouseGeo(ctx context.Context, userID typ.UserIdType, whAddressIDs []uint64) (map[uint64]entity.GeoLocation, fsserr.Error) {
	addressIDToResp, err := s.spexClient.BatchGetPrivateAddressWithGeocoding(ctx, userID, whAddressIDs)
	if err != nil {
		return nil, err
	}

	whAddressIDToGeo := make(map[uint64]entity.GeoLocation)
	for addressID, addr := range addressIDToResp {
		if addr.GetGeocoding().GetLongitude() == 0 && addr.GetGeocoding().GetLatitude() == 0 && addr.GetGeocoding().GetLocationType() == 0 {
			if addr.GetBasic().GetGeolocation().GetLongitude() == 0 && addr.GetBasic().GetGeolocation().GetLatitude() == 0 {
				Logger.CtxLogErrorf(ctx, "no geo-location, address_id: %d", addressID)
				return nil, fsserr.New(fsserr.ParamErr, "no geo-location")
			}
			whAddressIDToGeo[addressID] = entity.GeoLocation{
				Latitude:  addr.GetBasic().GetGeolocation().GetLatitude(),
				Longitude: addr.GetBasic().GetGeolocation().GetLongitude(),
			}
			continue
		}
		whAddressIDToGeo[addressID] = entity.GeoLocation{
			Latitude:  addr.GetGeocoding().GetLatitude(),
			Longitude: addr.GetGeocoding().GetLongitude(),
			Precision: uint8(addr.GetGeocoding().GetLocationType()),
		}
	}

	return whAddressIDToGeo, nil
}

func (s *AddrServiceImpl) getShippingAddresses(ctx context.Context, buyerID typ.UserIdType, sellerIDs []typ.UserIdType, managedBySBS bool, regionCodes []string) (*locationuserlocationpb.GetShippingAddressCombinationsResponse, fsserr.Error) {
	req := &locationuserlocationpb.GetShippingAddressCombinationsRequest{}
	if buyerID > 0 {
		req.BuyerUserId = proto.Int64(int64(buyerID))
	}
	if len(sellerIDs) > 0 {
		req.SellerUserIds = typ.ConvertIntegerSlices[typ.UserIdType, int64](sellerIDs)
	}
	if managedBySBS {
		req.WithWarehouse = proto.Bool(managedBySBS)
		req.WarehouseRegions = regionCodes
	}

	resp, err := s.spexClient.GetShippingAddresses(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *AddrServiceImpl) processRawWHAddressResponse(shippingAddressesResp *locationuserlocationpb.GetShippingAddressCombinationsResponse) map[string]*locationuserlocationpb.BuyerAddress {
	result := make(map[string]*locationuserlocationpb.BuyerAddress)

	addressMappings := shippingAddressesResp.WarehouseAddressMapping
	idToCode := make(map[int64]string)
	for _, mapping := range addressMappings {
		idToCode[mapping.GetAddressId()] = mapping.GetCode()
	}

	warehouseAddresses := shippingAddressesResp.WarehouseAddressList
	idToAddr := make(map[int64]*locationuserlocationpb.BuyerAddress)
	for _, addr := range warehouseAddresses {
		idToAddr[addr.GetId()] = addr
	}

	for id, addr := range idToAddr {
		result[idToCode[id]] = addr
	}

	return result
}
