// Package channel_generator 提供物流渠道生成服务
// 该服务负责根据店铺、商品信息和履约要求，生成每个商品可用的物流渠道列表
// 主要功能包括：
// 1. 基于店铺级别的渠道配置生成可用渠道
// 2. 基于商品级别的限制过滤渠道（如PFF验证、假冒商品过滤等）
// 3. 合并店铺级和商品级渠道配置，生成最终的商品-渠道映射
package channel_generator

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_info"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_logistics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

// ChannelGeneratorService 定义渠道生成服务的接口
// 该接口提供根据店铺商品信息和履约要求生成可用物流渠道的能力
type ChannelGeneratorService interface {
	// GenerateItemsToEnabledChannels 为指定的店铺商品生成可用的物流渠道映射
	// 参数:
	//   ctx: 上下文信息
	//   shopToItems: 店铺ID到商品ID列表的映射 map[shopID][]itemID
	//   fulfilmentInfo: 履约信息，包含仓库代码、是否SBS管理等
	// 返回:
	//   map[uint64]collection.Set[int]: 商品ID到可用渠道ID集合的映射
	//   error: 处理过程中的错误信息
	GenerateItemsToEnabledChannels(ctx context.Context, shopToItems map[uint64][]typ.ItemIdType, fulfilmentInfo entity.FulfillmentInfo) (map[uint64]collection.Set[int], error)
}

// GeneratorServiceImpl 渠道生成服务的具体实现
// 该结构体整合了多个依赖服务来完成渠道生成的复杂逻辑
type GeneratorServiceImpl struct {
	configGetter         config.ConfAccessor                 // 配置访问器，用于获取各种开关和配置项
	shopLogisticsService shop_logistics.ShopLogisticsService // 店铺物流服务，提供店铺级别的渠道信息
	itemService          item_info.ItemService               // 商品信息服务，提供商品详情和渠道验证
	itemTagService       item_tag.ItemTagService             // 商品标签服务，用于检查商品特殊标签（如假冒商品）
}

// NewChannelGeneratorServiceImpl 创建新的渠道生成服务实例
// 参数:
//
//	configGetter: 配置访问器
//	shopLogisticsService: 店铺物流服务
//	itemService: 商品信息服务
//	itemTagService: 商品标签服务
//
// 返回:
//
//	*GeneratorServiceImpl: 渠道生成服务实例
func NewChannelGeneratorServiceImpl(
	configGetter config.ConfAccessor,
	shopLogisticsService shop_logistics.ShopLogisticsService,
	itemService item_info.ItemService,
	itemTagService item_tag.ItemTagService) *GeneratorServiceImpl {
	return &GeneratorServiceImpl{
		configGetter:         configGetter,
		shopLogisticsService: shopLogisticsService,
		itemService:          itemService,
		itemTagService:       itemTagService}
}

// GenerateItemsToEnabledChannels 生成商品到可用渠道的映射
// 该方法是核心业务逻辑，分为以下步骤：
// 1. 获取店铺级别的可用渠道
// 2. 获取商品级别的可用渠道（包含PFF验证、假冒商品过滤等）
// 3. 取两者的交集作为最终结果
func (g *GeneratorServiceImpl) GenerateItemsToEnabledChannels(
	ctx context.Context,
	shopToItems map[uint64][]typ.ItemIdType,
	fulfilmentInfo entity.FulfillmentInfo,
) (map[uint64]collection.Set[int], error) {
	// 提取所有店铺ID
	shopIDs := make([]uint64, 0, len(shopToItems))
	for k := range shopToItems {
		shopIDs = append(shopIDs, k)
	}

	// 获取店铺级别的可用渠道
	shopLevelEnabledChannelIDs, err := g.shopLogisticsService.GenerateShopIDToEnabledChannelIDs(ctx, shopIDs, fulfilmentInfo)
	if err != nil {
		return nil, err
	}

	// 获取商品级别的可用渠道
	itemLevelEnabledChannelIDs, err := g.enabledChannelByShopItemID(
		ctx,
		ConvertShopItemsMapToShopItemIDs(shopToItems),
		fulfilmentInfo,
	)
	if err != nil {
		return nil, err
	}

	// 记录调试日志
	logger.CtxLogInfof(ctx,
		"GenerateItemsToEnabledChannels, shop_level_enabled_channel_ids=%v, item_level_enabled_channel_ids=%v",
		shopLevelEnabledChannelIDs, itemLevelEnabledChannelIDs,
	)

	// 构建最终的商品-渠道映射（取店铺级和商品级渠道的交集）
	return buildItemsToEnabledChannelsMap(shopToItems, shopLevelEnabledChannelIDs, itemLevelEnabledChannelIDs), nil
}

// enabledChannelByShopItemID 根据店铺商品ID获取商品级别的可用渠道
// 该方法包含以下逻辑：
// 1. 批量获取商品信息
// 2. 基于商品信息进行PFF验证，生成可用渠道
// 3. 过滤假冒商品的受限渠道
func (g *GeneratorServiceImpl) enabledChannelByShopItemID(
	ctx context.Context,
	shopItemIDs []entity.ShopItemID,
	fulfilmentInfo entity.FulfillmentInfo,
) (map[uint64][]int, error) {
	// 批量获取商品信息
	itemIDToInfo, err := g.itemService.BatchItemInfo(ctx, shopItemIDs)
	if err != nil {
		return nil, err
	}

	// 为每个商品生成可用渠道（包含PFF验证）
	itemIDToChannelIDs := make(map[typ.ItemIdType][]int)
	for shopItemID, itemInfo := range itemIDToInfo {
		itemIDToChannelIDs[shopItemID.ItemID] = g.itemService.EnabledChannelIDsWithPFFValidation(
			ctx,
			itemInfo,
			fulfilmentInfo.ManagedBySBS,
			[]string{fulfilmentInfo.WarehouseCode},
		)
	}

	// 过滤假冒商品的受限渠道
	itemIDToChannelIDs = g.batchFilterChannelByCounterfeitSKU(ctx, itemIDToChannelIDs)

	return itemIDToChannelIDs, nil
}

// batchFilterChannelByCounterfeitSKU 批量过滤假冒商品的受限渠道
// 假冒商品在某些渠道上有限制，需要从可用渠道中移除这些受限渠道
// 处理逻辑：
// 1. 检查配置开关是否启用假冒商品过滤
// 2. 批量检查商品是否包含假冒商品标签
// 3. 如果存在假冒商品，获取渠道配置并过滤不支持假冒商品的渠道
func (g *GeneratorServiceImpl) batchFilterChannelByCounterfeitSKU(ctx context.Context, itemIdToChannelIDs map[typ.ItemIdType][]int) map[typ.ItemIdType][]int {
	// 检查假冒商品渠道阻断开关是否启用
	if !g.configGetter.GetCounterfeitItemChannelBlockToggle(ctx) {
		return itemIdToChannelIDs
	}

	// 提取所有商品ID
	itemIds := make([]typ.ItemIdType, 0)
	for itemId := range itemIdToChannelIDs {
		itemIds = append(itemIds, itemId)
	}

	// 批量检查商品是否包含假冒商品标签
	itemIDToIsCounterfeit, err := g.itemTagService.BatchCheckItemContainsTag(ctx, itemIds, g.configGetter.GetCounterfeitItemLabelId(ctx))
	if err != nil {
		logger.CtxLogErrorf(ctx, "batchFilterChannelByCounterfeitSKU|BatchCheckItemContainsTag, err=%v", err)
		return itemIdToChannelIDs
	}

	// 检查是否存在假冒商品
	containCounterfeitSKU := false
	for _, isCounterfeit := range itemIDToIsCounterfeit {
		if isCounterfeit {
			containCounterfeitSKU = true
			break
		}
	}
	// 如果没有假冒商品，直接返回原始结果
	if !containCounterfeitSKU {
		return itemIdToChannelIDs
	}

	// 获取渠道配置信息
	channelIdToChannel, err := g.shopLogisticsService.GetChannelIdToChannel(ctx)
	if err != nil {
		logger.CtxLogErrorf(ctx, "batchFilterChannelByCounterfeitSKU|GetChannelIdToChannel, err=%v", err)
		return itemIdToChannelIDs
	}

	// 为假冒商品过滤不支持的渠道
	filteredItemIdToChannelIDs := make(map[uint64][]int, len(itemIdToChannelIDs))
	for itemId, channelIds := range itemIdToChannelIDs {
		filteredChannelIDs := channelIds
		// 如果是假冒商品，需要过滤不支持假冒商品的渠道
		if itemIDToIsCounterfeit[itemId] {
			filteredChannelIDs = make([]int, 0, len(channelIds))
			for _, channelID := range channelIds {
				// 只保留支持假冒商品的渠道
				if !channelIdToChannel[uint64(channelID)].NotSupportCounterfeitItem {
					filteredChannelIDs = append(filteredChannelIDs, channelID)
				}
			}
		}
		filteredItemIdToChannelIDs[itemId] = filteredChannelIDs
	}

	return filteredItemIdToChannelIDs
}

// ConvertShopItemsMapToShopItemIDs 将店铺-商品映射转换为店铺商品ID列表
// 参数:
//
//	shopToItems: 店铺ID到商品ID列表的映射
//
// 返回:
//
//	[]entity.ShopItemID: 店铺商品ID列表，每个元素包含店铺ID和商品ID
func ConvertShopItemsMapToShopItemIDs(shopToItems map[uint64][]typ.ItemIdType) []entity.ShopItemID {
	var shopItemIDs []entity.ShopItemID
	for shopID, itemIDs := range shopToItems {
		for _, itemID := range itemIDs {
			shopItemIDs = append(shopItemIDs, entity.ShopItemID{
				ShopID: shopID,
				ItemID: itemID,
			})
		}
	}
	return shopItemIDs
}

// buildItemsToEnabledChannelsMap 构建商品到可用渠道的最终映射
// 该函数取店铺级渠道和商品级渠道的交集，确保渠道既在店铺级别可用，也在商品级别可用
// 参数:
//
//	shopToItems: 店铺到商品的映射
//	shopLevelChannels: 店铺级别的可用渠道
//	itemLevelChannels: 商品级别的可用渠道
//
// 返回:
//
//	map[uint64]collection.Set[int]: 商品ID到可用渠道集合的映射
func buildItemsToEnabledChannelsMap(
	shopToItems map[uint64][]typ.ItemIdType,
	shopLevelChannels map[uint64][]int,
	itemLevelChannels map[typ.ItemIdType][]int,
) map[typ.ItemIdType]collection.Set[int] {
	itemsToEnabledChannels := make(map[typ.ItemIdType]collection.Set[int])
	for shopID, shopChannels := range shopLevelChannels {
		// 将店铺级渠道转换为集合
		shopChannelSet := collection.NewSetFromSlice(shopChannels)
		for _, item := range shopToItems[shopID] {
			// 将商品级渠道转换为集合
			itemChannelSet := collection.NewSetFromSlice(itemLevelChannels[item])
			// 取交集：只有同时在店铺级和商品级都可用的渠道才是最终可用的
			itemsToEnabledChannels[item] = shopChannelSet.Intersection(itemChannelSet)
		}
	}
	return itemsToEnabledChannels
}
