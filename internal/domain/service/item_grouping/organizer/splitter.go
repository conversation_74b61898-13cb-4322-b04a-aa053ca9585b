package organizer

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ItemSplitter[K comparable] interface {
	SplitItems(ctx context.Context, items []group_entity.ShippingOrderItem) (map[K][]group_entity.ShippingOrderItem, error)
}

type fulfilmentTypeItemSplitter struct {
	fulfilmentTypes     []constant.FulfilmentType
	stockLocationFilter func(group_entity.StockLocations) group_entity.StockLocations
}

func NewFulfilmentTypeItemSplitter(
	fulfilmentTypes []constant.FulfilmentType,
	stockLocationFilter func(group_entity.StockLocations) group_entity.StockLocations,
) ItemSplitter[constant.FulfilmentType] {
	return &fulfilmentTypeItemSplitter{
		fulfilmentTypes:     fulfilmentTypes,
		stockLocationFilter: stockLocationFilter,
	}
}

func (s *fulfilmentTypeItemSplitter) SplitItems(ctx context.Context, items []group_entity.ShippingOrderItem) (map[constant.FulfilmentType][]group_entity.ShippingOrderItem, error) {
	fulfilmentTypeToItems := make(map[constant.FulfilmentType][]group_entity.ShippingOrderItem)
	for _, item := range items {
		var totalStock uint32
		var fulfilledBySingleFulfilmentType bool
		fulfilmentTypeToStock := make(map[constant.FulfilmentType]uint32)

		for _, fulfilmentType := range s.fulfilmentTypes {
			stockLocations := item.GetStockLocations(fulfilmentType)
			if s.stockLocationFilter != nil {
				stockLocations = s.stockLocationFilter(stockLocations)
			}
			stock := stockLocations.TotalStock()
			fulfilmentTypeToStock[fulfilmentType] = stock
			if stock >= item.Quantity {
				fulfilmentTypeToItems[fulfilmentType] = append(fulfilmentTypeToItems[fulfilmentType], item)
				fulfilledBySingleFulfilmentType = true
				break
			}
			totalStock += stock
		}

		if fulfilledBySingleFulfilmentType {
			continue
		}
		if totalStock < item.Quantity {
			return nil, &fsserr.OutOfStockError{
				IsPackagePromotion: item.IsPackageItem(),
				ItemStockInfos:     group_entity.BuildOOSItemStockInfos(item, totalStock),
			}
		}

		remainingQuantity := item.Quantity
		for _, fulfilmentType := range s.fulfilmentTypes {
			if remainingQuantity <= 0 {
				break
			}

			splitItem := item
			if fulfilmentTypeToStock[fulfilmentType] >= remainingQuantity {
				splitItem.Quantity = remainingQuantity
			} else {
				splitItem.Quantity = fulfilmentTypeToStock[fulfilmentType]
			}
			fulfilmentTypeToItems[fulfilmentType] = append(fulfilmentTypeToItems[fulfilmentType], splitItem)
			remainingQuantity -= splitItem.Quantity
		}

		if remainingQuantity > 0 {
			return nil, &fsserr.OutOfStockError{
				IsPackagePromotion: item.IsPackageItem(),
				ItemStockInfos:     group_entity.BuildOOSItemStockInfos(item, totalStock),
			}
		}
	}

	return fulfilmentTypeToItems, nil
}

type regionItemSplitter struct {
	regionPriorityList []string
}

func NewRegionItemSplitter(regionPriorityList []string) ItemSplitter[string] {
	return &regionItemSplitter{
		regionPriorityList: regionPriorityList,
	}
}

func (r *regionItemSplitter) SplitItems(ctx context.Context, items []group_entity.ShippingOrderItem) (map[string][]group_entity.ShippingOrderItem, error) {
	regionToItems := make(map[string][]group_entity.ShippingOrderItem)
	for _, item := range items {
		regionToItem, err := r.splitItemByRegion(ctx, item)
		if err != nil {
			return nil, err
		}
		for region, item := range regionToItem {
			regionToItems[region] = append(regionToItems[region], item)
		}
	}

	return regionToItems, nil
}

func (r *regionItemSplitter) splitItemByRegion(ctx context.Context, item group_entity.ShippingOrderItem) (map[string]group_entity.ShippingOrderItem, error) {
	regionToTotalStock := make(map[string]uint32)
	var totalStock uint32
	for _, location := range item.GetStockLocations(constant.FulfilmentTypeShopee, constant.FulfilmentTypeSeller) {
		region := location.Region()
		if region == "" {
			Logger.CtxLogErrorf(ctx, "invalid source region, item: %s, source: %s", Logger.JsonString(item), location.Source)
			continue
		}
		regionToTotalStock[region] += location.AvailableStock
		totalStock += location.AvailableStock
	}
	if totalStock < item.Quantity {
		return nil, &fsserr.OutOfStockError{
			ItemStockInfos: group_entity.BuildOOSItemStockInfos(item, totalStock),
		}
	}

	regionToItem := make(map[string]group_entity.ShippingOrderItem)
	remainingStock := item.Quantity

	for _, region := range r.regionPriorityList {
		if remainingStock <= 0 {
			break
		}

		reducingStock := regionToTotalStock[region]
		if reducingStock <= 0 {
			continue
		}
		reducingStock = min(reducingStock, remainingStock)
		remainingStock -= reducingStock

		newItem := item
		newItem.Quantity = reducingStock
		regionToItem[region] = newItem
	}

	if remainingStock > 0 {
		newItem := item
		newItem.Quantity = remainingStock
		// empty region means all the order regions are not in the priority list
		regionToItem[""] = newItem
	}

	return regionToItem, nil
}
