package processor

import (
	"context"
	"fmt"
	"slices"
	"sort"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type Cb3pfShippingOrderProcessor struct {
	configAccessor           config.ConfAccessor
	itemOrganizer            organizer.ItemOrganizer
	shopService              shop.ShopService
	addressService           address.AddrService
	warehousePriorityService warehouse_priority.WarehousePriorityService
	shopLocalSIPService      shop_local_sip.ShopLocalSIPService
}

func NewCb3pfShippingOrderProcessor(
	configAccessor config.ConfAccessor,
	itemOrganizer organizer.ItemOrganizer,
	shopService shop.ShopService,
	addressService address.AddrService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	shopLocalSIPService shop_local_sip.ShopLocalSIPService,
) *Cb3pfShippingOrderProcessor {
	return &Cb3pfShippingOrderProcessor{
		configAccessor:           configAccessor,
		itemOrganizer:            itemOrganizer,
		shopService:              shopService,
		addressService:           addressService,
		warehousePriorityService: warehousePriorityService,
		shopLocalSIPService:      shopLocalSIPService,
	}
}

func (c *Cb3pfShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return CB3PFShippingOrderProcessorName
}

func (c *Cb3pfShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		slices.Contains(c.configAccessor.GetIGS3PFShopFlags(ctx), shippingOrder.ShopInfo().ShopFlag)
}

func (c *Cb3pfShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, error) {
	if c.configAccessor.GetEnable3PFIgnoreSellerTag(ctx) {
		return c.doProcessWithoutSellerTag(ctx, buyerInfo, shippingOrders)
	} else {
		return c.doProcessWithSellerTag(ctx, shippingOrders)
	}
}

func (c *Cb3pfShippingOrderProcessor) doProcessWithoutSellerTag(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, error) {
	cb3PFHybridOrders, cb3PFOnlyOrders, err := c.separateShippingOrders(shippingOrders)
	if err != nil {
		return nil, err
	}

	var processedOrders []group_entity.ShippingOrder
	for _, order := range append(cb3PFHybridOrders, cb3PFOnlyOrders...) {
		splitOrders, err := c.splitShippingOrderWithoutSellerTag(
			ctx,
			buyerInfo,
			order,
			len(order.ShopInfo().Warehouses) > 1,
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) doProcessWithSellerTag(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, error) {
	cb3PFHybridOrders, cb3PFOnlyOrders, err := c.separateShippingOrders(shippingOrders)
	if err != nil {
		return nil, err
	}

	var processedOrders []group_entity.ShippingOrder
	for _, order := range append(cb3PFHybridOrders, cb3PFOnlyOrders...) {
		splitOrders, err := c.splitShippingOrderWithSellerTag(
			ctx,
			order,
			constant.ItemOrganizeStrategyMinimumParcel,
			len(order.ShopInfo().Warehouses) > 1,
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) separateShippingOrders(
	shippingOrders []group_entity.ShippingOrder,
) (cb3PFHybridOrders []group_entity.ShippingOrder, cb3PFOnlyOrders []group_entity.ShippingOrder, err error) {
	for _, order := range shippingOrders {
		if len(order.ShopInfo().Warehouses) == 0 {
			return nil, nil, fsserr.Err3PFShopNoWH
		}

		if len(order.ShopInfo().Warehouses) > 1 {
			cb3PFHybridOrders = append(cb3PFHybridOrders, order)
		} else {
			cb3PFOnlyOrders = append(cb3PFOnlyOrders, order)
		}
	}

	return cb3PFHybridOrders, cb3PFOnlyOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) getShopStrategy(
	ctx context.Context,
	shopIDs []uint64,
) map[uint64]constant.ItemOrganizeStrategy {
	return c.warehousePriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDs,
		constant.ItemOrganizeStrategyWarehousePriority,
	)
}

func (c *Cb3pfShippingOrderProcessor) splitShippingOrderWithoutSellerTag(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	is3PFHybrid bool,
) ([]group_entity.ShippingOrder, error) {
	regionToItems := make(map[string][]group_entity.ShippingOrderItem)
	for _, item := range shippingOrder.Items {
		region := c.getSourceRegion(fmt.Sprint(item.GetShopInfo().ShopID))
		if region == "" {
			continue
		}
		regionToItems[region] = append(regionToItems[region], item)
	}

	var splitOrders []group_entity.ShippingOrder
	for region, items := range regionToItems {
		shopeeItems, sellerItems, err := c.splitItemsByFulfilmentType(ctx, items, region)
		if err != nil {
			return nil, err
		}

		if len(shopeeItems) > 0 {
			shopeeOrders, err := c.organizerShopeeItems(ctx, buyerInfo, shippingOrder, shopeeItems, region)
			if err != nil {
				return nil, err
			}
			splitOrders = append(splitOrders, shopeeOrders...)
		}
		if len(sellerItems) > 0 {
			sellerOrders, err := c.organizerSellerItems(ctx, buyerInfo, shippingOrder, sellerItems, region)
			if err != nil {
				return nil, err
			}
			splitOrders = append(splitOrders, sellerOrders...)
		}
	}

	c.generateMetrics(ctx, is3PFHybrid, len(splitOrders))
	return splitOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) splitShippingOrderWithSellerTag(
	ctx context.Context,
	shippingOrder group_entity.ShippingOrder,
	strategy constant.ItemOrganizeStrategy,
	is3PFHybrid bool,
) ([]group_entity.ShippingOrder, error) {
	sourceToShopWarehouse := make(map[string]entity.ShopWarehouse)
	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToShopWarehouse[warehouse.LocationID] = warehouse
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		c.selectSourceFuncWithSellerTag(ctx, is3PFHybrid, sourceToShopWarehouse),
		c.sourcePrioritizeFuncWithSellerTag(ctx, is3PFHybrid, sourceToShopWarehouse),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	splitOrders := organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	c.generateMetrics(ctx, is3PFHybrid, len(splitOrders))
	return splitOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) splitItemsByFulfilmentType(
	ctx context.Context,
	items []group_entity.ShippingOrderItem,
	region string,
) ([]group_entity.ShippingOrderItem, []group_entity.ShippingOrderItem, error) {
	var shopeeItems, sellerItems []group_entity.ShippingOrderItem
	for _, item := range items {
		shopeeStock := c.filterLocationsByRegion(ctx, item.ShopeeStocks(), region).TotalStock()
		sellerStock := c.filterLocationsByRegion(ctx, item.SellerStocks(), region).TotalStock()

		if shopeeStock >= item.Quantity {
			shopeeItems = append(shopeeItems, item)
			continue
		}
		if sellerStock >= item.Quantity {
			sellerItems = append(sellerItems, item)
			continue
		}
		totalStock := shopeeStock + sellerStock
		if shopeeStock+sellerStock < item.Quantity {
			return nil, nil, &fsserr.OutOfStockError{
				ItemStockInfos: group_entity.BuildOOSItemStockInfos(item, totalStock),
			}
		}

		shopeeItem := item
		shopeeItem.Quantity = shopeeStock
		shopeeItems = append(shopeeItems, shopeeItem)
		sellerItem := item
		sellerItem.Quantity = item.Quantity - shopeeStock
		sellerItems = append(sellerItems, sellerItem)
	}

	return shopeeItems, sellerItems, nil
}

func (c *Cb3pfShippingOrderProcessor) organizerShopeeItems(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	items []group_entity.ShippingOrderItem,
	region string,
) ([]group_entity.ShippingOrder, error) {
	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		items,
		c.shopeeSourceSelect(ctx, region),
		c.shopeeSourcePrioritize(ctx, buyerInfo.Address),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == constant.FulfilmentTypeShopee {
			addr, err := c.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = addr.AddressID
		}
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (c *Cb3pfShippingOrderProcessor) organizerSellerItems(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	items []group_entity.ShippingOrderItem,
	region string,
) ([]group_entity.ShippingOrder, error) {
	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		items,
		c.sellerSourceSelect(ctx, region),
		c.sellerSourcePrioritize(ctx, buyerInfo, shippingOrder.ShopInfo()),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (c *Cb3pfShippingOrderProcessor) sellerSourcePrioritize(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopInfo group_entity.ShopInfo,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		if len(sources) <= 1 {
			return sources, nil
		}
		buyerAddressGeo, err := c.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		return c.warehousePriorityService.PrioritizeSellerMultiWarehouse(
			ctx,
			buyerAddressGeo,
			shopInfo.SellerID,
			shopInfo.Warehouses,
			sources,
		), nil
	}
}

func (c *Cb3pfShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, error) {
	localSIPInfo, err := c.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get local SIP shop info error: %v", err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = c.addressService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = c.addressService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (c *Cb3pfShippingOrderProcessor) sourcePrioritizeFuncWithSellerTag(
	ctx context.Context,
	is3PFHybrid bool,
	sourceToShopWarehouse map[string]entity.ShopWarehouse,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		if is3PFHybrid {
			var localSources, overseasSources []string
			for _, source := range sources {
				if sourceToShopWarehouse[source].IsLocalWarehouse(ctx) {
					localSources = append(localSources, source)
				} else {
					overseasSources = append(overseasSources, source)
				}
			}
			sort.SliceStable(localSources, func(i, j int) bool {
				return localSources[i] < localSources[j]
			})
			sort.SliceStable(overseasSources, func(i, j int) bool {
				return overseasSources[i] < overseasSources[j]
			})
			return append(localSources, overseasSources...), nil
		}

		sort.SliceStable(sources, func(i, j int) bool {
			return sources[i] < sources[j]
		})
		return sources, nil
	}
}

func (c *Cb3pfShippingOrderProcessor) shopeeSourceSelect(ctx context.Context, region string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, error) {
		return c.filterLocationsByRegion(ctx, item.ShopeeStocks(), region), nil
	}
}

func (c *Cb3pfShippingOrderProcessor) shopeeSourcePrioritize(ctx context.Context, buyerAddress entity.ItemGroupBuyerAddress) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		return c.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
	}
}

func (c *Cb3pfShippingOrderProcessor) sellerSourceSelect(ctx context.Context, region string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, error) {
		return c.filterLocationsByRegion(ctx, item.SellerStocks(), region), nil
	}
}

func (c *Cb3pfShippingOrderProcessor) filterLocationsByRegion(ctx context.Context, locations group_entity.StockLocations, region string) group_entity.StockLocations {
	var filteredLocations group_entity.StockLocations
	for _, location := range locations {
		if c.getSourceRegion(location.Source) == region {
			filteredLocations = append(filteredLocations, location)
		}
	}
	return filteredLocations
}

func (c *Cb3pfShippingOrderProcessor) getSourceRegion(source string) string {
	if len(source) < 2 {
		return ""
	}
	return strings.ToUpper(source[:2])
}

func (c *Cb3pfShippingOrderProcessor) generateMetrics(ctx context.Context, is3PFHybrid bool, count int) {
	if is3PFHybrid {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderType3PFHybrid, count)
	} else {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderType3PFOnly, count)
	}
}

func (c *Cb3pfShippingOrderProcessor) selectSourceFuncWithSellerTag(
	ctx context.Context,
	is3PFHybrid bool,
	sourceToShopWarehouse map[string]entity.ShopWarehouse,
) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, error) {
		if is3PFHybrid {
			var validateStock []group_entity.ItemStockLocation
			var localStockCount int
			for _, location := range item.SellerStocks() {
				if sourceToShopWarehouse[location.Source].AddressID != 0 {
					validateStock = append(validateStock, location)
					if sourceToShopWarehouse[location.Source].IsLocalWarehouse(ctx) {
						localStockCount++
					}
				}
			}

			if len(validateStock) == 0 {
				Logger.CtxLogErrorf(ctx, "3PF hybrid item has no valid stock, item: %s", Logger.JsonString(item))
				return nil, fsserr.Err3PFShopNoWH
			}
			if localStockCount > 1 {
				Logger.CtxLogErrorf(ctx, "3PF hybrid item has more than 1 local stock, item: %s", Logger.JsonString(item))
				return nil, fsserr.Err3PFShopNoWH
			}

			return validateStock, nil
		}

		if len(item.SellerStocks()) != 1 {
			Logger.CtxLogErrorf(ctx, "3PF only item should only have 1 stock, item: %s", Logger.JsonString(item))
			return nil, fsserr.Err3PFShopNoWH
		}

		return item.SellerStocks(), nil
	}
}
