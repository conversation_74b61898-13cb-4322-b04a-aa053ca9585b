package processor

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// GroupingRulesProcessor 定义分组规则处理器接口
// 这个接口在领域层定义，实现统一的分组规则处理逻辑
type GroupingRulesProcessor interface {
	// ApplyRules 应用分组规则
	// 参数:
	//   - ctx: 上下文
	//   - shippingOrders: 经过库存寻源处理器处理后的订单列表
	//   - rules: 分组规则领域实体
	// 返回: 应用规则后的订单列表和错误信息
	//
	// 注意: 如果 rules 为 nil 或 empty，实现应该直接返回原始订单，不做任何处理
	ApplyRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error)

	// ApplyBundleRules 单独应用捆绑规则
	// 参数:
	//   - ctx: 上下文
	//   - shippingOrders: 订单列表
	//   - bundleRules: 捆绑规则列表
	// 返回: 应用捆绑规则后的订单列表和错误信息
	ApplyBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, bundleRules []group_entity.BundleRule) ([]group_entity.ShippingOrder, fsserr.Error)

	// ApplyNonBundleRules 应用除捆绑规则之外的其他规则
	// 参数:
	//   - ctx: 上下文
	//   - shippingOrders: 订单列表
	//   - rules: 分组规则领域实体（仅处理非 BundleRule 部分）
	// 返回: 应用规则后的订单列表和错误信息
	ApplyNonBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error)
}

// GroupingRulesProcessorImpl 领域层的分组规则处理器实现
type GroupingRulesProcessorImpl struct{}

func NewGroupingRulesProcessor() *GroupingRulesProcessorImpl {
	return &GroupingRulesProcessorImpl{}
}

// ApplyRules 实现 GroupingRulesProcessor 接口
func (p *GroupingRulesProcessorImpl) ApplyRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error) {
	// 检查规则是否为空
	if rules == nil || rules.IsEmpty() {
		// 如果规则为空，直接返回原始订单
		return shippingOrders, nil
	}

	// 应用分组规则逻辑
	return p.applyGroupingRules(ctx, shippingOrders, rules)
}

// ApplyBundleRules 实现单独应用捆绑规则
func (p *GroupingRulesProcessorImpl) ApplyBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, bundleRules []group_entity.BundleRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	if len(bundleRules) == 0 || len(shippingOrders) == 0 {
		return shippingOrders, nil
	}

	// 应用捆绑规则
	return p.applyBundleRules(ctx, shippingOrders, bundleRules)
}

// ApplyNonBundleRules 实现应用除捆绑规则之外的其他规则
func (p *GroupingRulesProcessorImpl) ApplyNonBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error) {
	if rules == nil || len(shippingOrders) == 0 {
		return shippingOrders, nil
	}

	// 检查是否有非捆绑规则需要处理
	hasNonBundleRules := len(rules.ConstraintRules) > 0 || len(rules.IsolationRules) > 0 || len(rules.ChannelRules) > 0
	if !hasNonBundleRules {
		return shippingOrders, nil
	}

	var err fsserr.Error
	processedOrders := shippingOrders

	// 按照技术设计文档的顺序应用非捆绑规则：
	// 1. 约束规则 (ConstraintRule) - 确保商品级别的约束
	// 2. 隔离规则 (IsolationRule) - 将需要隔离的商品分开
	// 3. 渠道规则 (ChannelRule) - 基于物流渠道的规则

	// 1. 应用约束规则
	if len(rules.ConstraintRules) > 0 {
		processedOrders, err = p.applyConstraintRules(ctx, processedOrders, rules.ConstraintRules)
		if err != nil {
			return nil, err
		}
	}

	// 2. 应用隔离规则
	if len(rules.IsolationRules) > 0 {
		processedOrders, err = p.applyIsolationRules(ctx, processedOrders, rules.IsolationRules)
		if err != nil {
			return nil, err
		}
	}

	// 3. 应用渠道规则
	if len(rules.ChannelRules) > 0 {
		processedOrders, err = p.applyChannelRules(ctx, processedOrders, rules.ChannelRules)
		if err != nil {
			return nil, err
		}
	}

	return processedOrders, nil
}

// applyGroupingRules 根据GroupingRules对处理后的ShippingOrder进行拆合单
func (p *GroupingRulesProcessorImpl) applyGroupingRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules *group_entity.GroupingRules) ([]group_entity.ShippingOrder, fsserr.Error) {
	if rules == nil || len(shippingOrders) == 0 {
		return shippingOrders, nil
	}

	var err fsserr.Error
	processedOrders := shippingOrders

	// 按照技术设计文档的顺序应用规则：
	// 1. 首先应用约束规则 (ConstraintRule) - 确保商品级别的约束
	// 2. 然后应用隔离规则 (IsolationRule) - 将需要隔离的商品分开
	// 3. 接着应用捆绑规则 (BundleRule) - 将需要捆绑的商品合并
	// 4. 最后应用渠道规则 (ChannelRule) - 基于物流渠道的规则

	// 1. 应用约束规则
	if len(rules.ConstraintRules) > 0 {
		processedOrders, err = p.applyConstraintRules(ctx, processedOrders, rules.ConstraintRules)
		if err != nil {
			return nil, err
		}
	}

	// 2. 应用隔离规则
	if len(rules.IsolationRules) > 0 {
		processedOrders, err = p.applyIsolationRules(ctx, processedOrders, rules.IsolationRules)
		if err != nil {
			return nil, err
		}
	}

	// 3. 应用捆绑规则
	if len(rules.BundleRules) > 0 {
		processedOrders, err = p.applyBundleRules(ctx, processedOrders, rules.BundleRules)
		if err != nil {
			return nil, err
		}
	}

	// 4. 应用渠道规则
	if len(rules.ChannelRules) > 0 {
		processedOrders, err = p.applyChannelRules(ctx, processedOrders, rules.ChannelRules)
		if err != nil {
			return nil, err
		}
	}

	return processedOrders, nil
}

// applyConstraintRules 应用约束规则
func (p *GroupingRulesProcessorImpl) applyConstraintRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules []group_entity.ConstraintRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	// 检查约束规则是否可以满足
	for _, rule := range rules {
		if err := p.validateConstraintRule(ctx, shippingOrders, rule); err != nil {
			if rule.Mandatory {
				return nil, err
			}
			// 非强制性规则，记录警告后继续
			logger.CtxLogInfof(ctx, "constraint rule %s not met: %v", rule.RuleID, err)
		}
	}

	// 约束规则主要是验证，不改变订单结构
	return shippingOrders, nil
}

// validateConstraintRule 验证约束规则
func (p *GroupingRulesProcessorImpl) validateConstraintRule(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.ConstraintRule) fsserr.Error {
	switch rule.ConstraintType {
	case group_entity.ConstraintTypeDisableQuantitySplit:
		return p.validateNoQuantitySplit(ctx, shippingOrders, rule)
	case group_entity.ConstraintTypeSingleSourceOnly:
		return p.validateSingleSourceOnly(ctx, shippingOrders, rule)
	default:
		return fsserr.New(fsserr.ParamErr, "unsupported constraint type: %d", rule.ConstraintType)
	}
}

// validateNoQuantitySplit 验证禁止数量拆分约束
func (p *GroupingRulesProcessorImpl) validateNoQuantitySplit(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.ConstraintRule) fsserr.Error {
	queryIDSet := collection.NewSetFromSlice(rule.ItemQueryIDs)

	for _, order := range shippingOrders {
		for _, item := range order.Items {
			if queryIDSet.Contains(item.QueryId) {
				// 检查该商品是否被拆分到多个订单中
				if p.isItemSplitAcrossOrders(item.QueryId, shippingOrders) {
					return fsserr.New(fsserr.ParamErr, "item %s violates no quantity split constraint", item.QueryId)
				}
			}
		}
	}
	return nil
}

// validateSingleSourceOnly 验证单一库存源约束
func (p *GroupingRulesProcessorImpl) validateSingleSourceOnly(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.ConstraintRule) fsserr.Error {
	queryIDSet := collection.NewSetFromSlice(rule.ItemQueryIDs)
	queryIDToSource := make(map[string]string)

	for _, order := range shippingOrders {
		source := order.FulfilmentInfo.Source
		for _, item := range order.Items {
			if queryIDSet.Contains(item.QueryId) {
				if existingSource, exists := queryIDToSource[item.QueryId]; exists {
					if existingSource != source {
						return fsserr.New(fsserr.ParamErr, "item %s violates single source constraint", item.QueryId)
					}
				} else {
					queryIDToSource[item.QueryId] = source
				}
			}
		}
	}
	return nil
}

// isItemSplitAcrossOrders 检查商品是否被拆分到多个订单
func (p *GroupingRulesProcessorImpl) isItemSplitAcrossOrders(queryId string, shippingOrders []group_entity.ShippingOrder) bool {
	foundInOrderCount := 0
	for _, order := range shippingOrders {
		for _, item := range order.Items {
			if item.QueryId == queryId {
				foundInOrderCount++
				break // 该订单中找到了，跳出内层循环
			}
		}
	}
	return foundInOrderCount > 1
}

// applyIsolationRules 应用隔离规则
func (p *GroupingRulesProcessorImpl) applyIsolationRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules []group_entity.IsolationRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	var result []group_entity.ShippingOrder
	processedQueryIDs := collection.NewSet[string]()

	// 按隔离规则处理
	for _, rule := range rules {
		isolatedOrders, remainingOrders, err := p.applyIsolationRule(ctx, shippingOrders, rule)
		if err != nil {
			if rule.Mandatory {
				return nil, err
			}
			// 非强制性规则，失败时继续处理
			continue
		}

		// 添加隔离出来的订单
		result = append(result, isolatedOrders...)

		// 记录已处理的QueryID
		for _, queryID := range rule.ItemQueryIDs {
			processedQueryIDs.Add(queryID)
		}

		// 更新剩余待处理的订单
		shippingOrders = remainingOrders
	}

	// 添加未被隔离规则处理的订单
	result = append(result, shippingOrders...)

	return result, nil
}

// applyIsolationRule 应用单个隔离规则
func (p *GroupingRulesProcessorImpl) applyIsolationRule(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.IsolationRule) ([]group_entity.ShippingOrder, []group_entity.ShippingOrder, fsserr.Error) {
	queryIDSet := collection.NewSetFromSlice(rule.ItemQueryIDs)
	var isolatedOrders []group_entity.ShippingOrder
	var remainingOrders []group_entity.ShippingOrder

	for _, order := range shippingOrders {
		isolatedItems, remainingItems := p.separateItemsByQueryIDs(order.Items, queryIDSet)

		// 检查隔离条件
		if len(isolatedItems) > 0 {
			if !p.checkIsolationConditions(order, rule.Conditions) {
				if rule.Mandatory {
					return nil, nil, fsserr.New(fsserr.ParamErr, "isolation rule %s conditions not met", rule.RuleID)
				}
				// 条件不满足，不进行隔离
				remainingOrders = append(remainingOrders, order)
				continue
			}

			// 根据隔离类型处理
			switch rule.IsolationType {
			case group_entity.IsolationTypeSingleItem:
				// 每个商品独立包裹
				for _, item := range isolatedItems {
					newOrder := order.SplitNew([]group_entity.ShippingOrderItem{item})
					isolatedOrders = append(isolatedOrders, newOrder)
				}
			case group_entity.IsolationTypeIsolatedGroup:
				// 符合条件的商品组成独立分组
				if len(isolatedItems) > 0 {
					newOrder := order.SplitNew(isolatedItems)
					isolatedOrders = append(isolatedOrders, newOrder)
				}
			default:
				return nil, nil, fsserr.New(fsserr.ParamErr, "unsupported isolation type: %d", rule.IsolationType)
			}
		}

		// 处理剩余商品
		if len(remainingItems) > 0 {
			newOrder := order.SplitNew(remainingItems)
			remainingOrders = append(remainingOrders, newOrder)
		}
	}

	return isolatedOrders, remainingOrders, nil
}

// separateItemsByQueryIDs 根据QueryID分离商品
func (p *GroupingRulesProcessorImpl) separateItemsByQueryIDs(items []group_entity.ShippingOrderItem, queryIDSet collection.Set[string]) ([]group_entity.ShippingOrderItem, []group_entity.ShippingOrderItem) {
	var matchedItems, remainingItems []group_entity.ShippingOrderItem

	for _, item := range items {
		if queryIDSet.Contains(item.QueryId) {
			matchedItems = append(matchedItems, item)
		} else {
			remainingItems = append(remainingItems, item)
		}
	}

	return matchedItems, remainingItems
}

// checkIsolationConditions 检查隔离条件
func (p *GroupingRulesProcessorImpl) checkIsolationConditions(order group_entity.ShippingOrder, conditions []group_entity.RuleCondition) bool {
	for _, condition := range conditions {
		if !p.checkRuleCondition(order, condition) {
			return false
		}
	}
	return true
}

// checkRuleCondition 检查单个规则条件
func (p *GroupingRulesProcessorImpl) checkRuleCondition(order group_entity.ShippingOrder, condition group_entity.RuleCondition) bool {
	switch condition.ConditionType {
	case group_entity.ConditionTypeChannelEnabled:
		return p.checkChannelEnabled(order, condition.ChannelIDs)
	default:
		return false
	}
}

// checkChannelEnabled 检查渠道是否启用
func (p *GroupingRulesProcessorImpl) checkChannelEnabled(order group_entity.ShippingOrder, channelIDs []int64) bool {
	// 获取订单中所有商品的可用渠道
	availableChannels := collection.NewSet[int64]()

	for _, item := range order.Items {
		for _, stockLocation := range item.FulfilmentTypeToLocations {
			for _, location := range stockLocation {
				for _, channelID := range location.EnabledChannels {
					availableChannels.Add(int64(channelID))
				}
			}
		}
	}

	// 检查指定的渠道是否可用
	for _, channelID := range channelIDs {
		if availableChannels.Contains(channelID) {
			return true
		}
	}
	return false
}

// applyBundleRules 应用捆绑规则
func (p *GroupingRulesProcessorImpl) applyBundleRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules []group_entity.BundleRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	// 对每个捆绑规则进行处理
	for _, rule := range rules {
		var err fsserr.Error
		shippingOrders, err = p.applyBundleRule(ctx, shippingOrders, rule)
		if err != nil {
			if rule.Mandatory {
				return nil, err
			}
			// 非强制性规则，失败时继续
		}
	}

	return shippingOrders, nil
}

// applyBundleRule 应用单个捆绑规则
func (p *GroupingRulesProcessorImpl) applyBundleRule(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.BundleRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	queryIDSet := collection.NewSetFromSlice(rule.ItemQueryIDs)

	// 找到需要捆绑的商品
	var bundleItems []group_entity.ShippingOrderItem
	var bundleOrder *group_entity.ShippingOrder
	var resultOrders []group_entity.ShippingOrder

	for _, order := range shippingOrders {
		matchedItems, remainingItems := p.separateItemsByQueryIDs(order.Items, queryIDSet)

		if len(matchedItems) > 0 {
			bundleItems = append(bundleItems, matchedItems...)
			if bundleOrder == nil {
				// 使用第一个包含目标商品的订单作为捆绑订单的基础
				tempOrder := order
				bundleOrder = &tempOrder
			}
		}

		// 处理剩余商品
		if len(remainingItems) > 0 {
			newOrder := order.SplitNew(remainingItems)
			resultOrders = append(resultOrders, newOrder)
		}
	}

	// 检查是否找到了所有需要捆绑的商品
	if len(bundleItems) == 0 {
		// 没有找到需要捆绑的商品，返回原订单
		return shippingOrders, nil
	}

	foundQueryIDs := collection.NewSet[string]()
	for _, item := range bundleItems {
		foundQueryIDs.Add(item.QueryId)
	}

	// 检查是否所有指定的商品都找到了
	for _, queryID := range rule.ItemQueryIDs {
		if !foundQueryIDs.Contains(queryID) {
			if rule.Mandatory {
				return nil, fsserr.New(fsserr.ParamErr, "bundle rule %s cannot find item %s", rule.RuleID, queryID)
			}
			// 非强制性规则，部分商品未找到时不执行捆绑
			return shippingOrders, nil
		}
	}

	// 验证捆绑的可行性 (例如：检查库存源兼容性)
	if err := p.validateBundleCompatibility(bundleItems); err != nil {
		if rule.Mandatory {
			return nil, err
		}
		// 非强制性规则，不兼容时不执行捆绑
		return shippingOrders, nil
	}

	// 创建捆绑订单
	if bundleOrder != nil {
		newBundleOrder := bundleOrder.SplitNew(bundleItems)
		resultOrders = append(resultOrders, newBundleOrder)
	}

	return resultOrders, nil
}

// validateBundleCompatibility 验证捆绑兼容性
func (p *GroupingRulesProcessorImpl) validateBundleCompatibility(items []group_entity.ShippingOrderItem) fsserr.Error {
	if len(items) == 0 {
		return nil
	}

	// 检查所有商品是否来自同一个店铺（简化实现）
	firstShopID := items[0].GetShopInfo().ShopID
	for _, item := range items[1:] {
		if item.GetShopInfo().ShopID != firstShopID {
			return fsserr.New(fsserr.ParamErr, "bundle items must be from the same shop")
		}
	}

	// 可以添加更多兼容性检查，例如：
	// - 检查库存源是否兼容
	// - 检查物流渠道是否兼容
	// - 检查地理位置是否兼容

	return nil
}

// applyChannelRules 应用渠道规则
func (p *GroupingRulesProcessorImpl) applyChannelRules(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rules []group_entity.ChannelRule) ([]group_entity.ShippingOrder, fsserr.Error) {
	for _, rule := range rules {
		if err := p.validateChannelRule(ctx, shippingOrders, rule); err != nil {
			if rule.Mandatory {
				return nil, err
			}
			// 非强制性规则，验证失败时继续
		}
	}

	// 渠道规则主要是验证，不改变订单结构
	return shippingOrders, nil
}

// validateChannelRule 验证渠道规则
func (p *GroupingRulesProcessorImpl) validateChannelRule(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.ChannelRule) fsserr.Error {
	switch rule.RuleType {
	case group_entity.ChannelRuleTypeCommonChannelRequired:
		return p.validateCommonChannelRequired(ctx, shippingOrders, rule)
	case group_entity.ChannelRuleTypeChannelExclusive:
		return p.validateChannelExclusive(ctx, shippingOrders, rule)
	default:
		return fsserr.New(fsserr.ParamErr, "unsupported channel rule type: %d", rule.RuleType)
	}
}

// validateCommonChannelRequired 验证需要公共渠道
func (p *GroupingRulesProcessorImpl) validateCommonChannelRequired(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.ChannelRule) fsserr.Error {
	queryIDSet := collection.NewSetFromSlice(rule.ItemQueryIDs)
	var targetItems []group_entity.ShippingOrderItem

	// 收集目标商品
	for _, order := range shippingOrders {
		for _, item := range order.Items {
			if queryIDSet.Contains(item.QueryId) {
				targetItems = append(targetItems, item)
			}
		}
	}

	if len(targetItems) <= 1 {
		return nil // 只有一个商品不需要检查公共渠道
	}

	// 查找公共可用渠道
	commonChannels := p.findCommonChannels(targetItems)

	// 检查规则条件中的渠道是否在公共渠道中
	hasValidChannel := false
	for _, condition := range rule.Conditions {
		if condition.ConditionType == group_entity.ConditionTypeChannelEnabled {
			for _, channelID := range condition.ChannelIDs {
				if commonChannels.Contains(channelID) {
					hasValidChannel = true
					break
				}
			}
		}
		if hasValidChannel {
			break
		}
	}

	if !hasValidChannel {
		return fsserr.New(fsserr.ParamErr, "channel rule %s: no common valid channel found", rule.RuleID)
	}

	return nil
}

// validateChannelExclusive 验证渠道排他性
func (p *GroupingRulesProcessorImpl) validateChannelExclusive(ctx context.Context, shippingOrders []group_entity.ShippingOrder, rule group_entity.ChannelRule) fsserr.Error {
	// 渠道排他性验证的简化实现
	// 在实际项目中，这里需要根据具体的业务逻辑来实现
	return nil
}

// findCommonChannels 查找商品的公共可用渠道
func (p *GroupingRulesProcessorImpl) findCommonChannels(items []group_entity.ShippingOrderItem) collection.Set[int64] {
	if len(items) == 0 {
		return collection.NewSet[int64]()
	}

	// 获取第一个商品的所有可用渠道
	commonChannels := p.getItemAvailableChannels(items[0])

	// 与其他商品的可用渠道求交集
	for _, item := range items[1:] {
		itemChannels := p.getItemAvailableChannels(item)
		commonChannels = p.intersectChannels(commonChannels, itemChannels)
	}

	return commonChannels
}

// getItemAvailableChannels 获取商品的所有可用渠道
func (p *GroupingRulesProcessorImpl) getItemAvailableChannels(item group_entity.ShippingOrderItem) collection.Set[int64] {
	channels := collection.NewSet[int64]()

	for _, stockLocations := range item.FulfilmentTypeToLocations {
		for _, location := range stockLocations {
			for _, channelID := range location.EnabledChannels {
				channels.Add(int64(channelID))
			}
		}
	}

	return channels
}

// intersectChannels 计算两个渠道集合的交集
func (p *GroupingRulesProcessorImpl) intersectChannels(set1, set2 collection.Set[int64]) collection.Set[int64] {
	// 使用Set接口提供的Intersection方法
	return set1.Intersection(set2)
}
