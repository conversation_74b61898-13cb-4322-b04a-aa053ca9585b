package processor

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
)

type ShippingOrderProcessor interface {
	Name() ShippingOrderProcessorName
	IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool
	DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, error)
}

type ShippingOrderProcessorName string

const (
	AdvanceBookingShippingOrderProcessorName ShippingOrderProcessorName = "AdvanceBooking"
	CBLFFShippingOrderProcessorName          ShippingOrderProcessorName = "CBLFF"
	ResellShippingOrderProcessorName         ShippingOrderProcessorName = "Resell"
	ShopeeShippingOrderProcessorName         ShippingOrderProcessorName = "Shopee"
	CB3PFShippingOrderProcessorName          ShippingOrderProcessorName = "CB3PF"
	SellerShippingOrderProcessorName         ShippingOrderProcessorName = "Seller"
	WeightDimensionProcessorName             ShippingOrderProcessorName = "WeightDimension"
	PFFShippingOrderProcessorName            ShippingOrderProcessorName = "PFF"
	GroupShipmentProcessorName               ShippingOrderProcessorName = "GroupShipment"
)
