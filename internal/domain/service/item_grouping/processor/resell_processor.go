package processor

import (
	"context"
	"slices"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/organizer"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

func NewResellShippingOrderProcessor(
	confAccessor config.ConfAccessor,
	itemOrganizer organizer.ItemOrganizer,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	addressService address.AddrService,
	shopLocalSIPService shop_local_sip.ShopLocalSIPService,
) *ResellShippingOrderProcessor {
	return &ResellShippingOrderProcessor{
		configAccessor:           confAccessor,
		itemOrganizer:            itemOrganizer,
		warehousePriorityService: warehousePriorityService,
		addressService:           addressService,
		shopLocalSIPService:      shopLocalSIPService,
	}
}

type ResellShippingOrderProcessor struct {
	configAccessor           config.ConfAccessor
	itemOrganizer            organizer.ItemOrganizer
	warehousePriorityService warehouse_priority.WarehousePriorityService
	addressService           address.AddrService
	shopLocalSIPService      shop_local_sip.ShopLocalSIPService
}

func (r *ResellShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return ResellShippingOrderProcessorName
}

func (r *ResellShippingOrderProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		slices.Contains(r.configAccessor.GetIGSResellModeShopFlags(ctx), shippingOrder.ShopInfo().ShopFlag)
}

func (r *ResellShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, error) {
	//fs := metrics.StartFunctionSpan("itemgrouping.ResellShippingOrderProcessor.DoProcess")
	//defer fs.Finish(ctx)

	var processedOrders []group_entity.ShippingOrder
	for _, order := range shippingOrders {
		splitOrders, err := r.splitSingleShippingOrder(ctx, buyerInfo, order)
		if err != nil {
			return nil, err
		}

		processedOrders = append(processedOrders, splitOrders...)
	}
	return processedOrders, nil
}

func (r *ResellShippingOrderProcessor) splitSingleShippingOrder(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
) ([]group_entity.ShippingOrder, error) {
	regionPriorityList := r.getRegionPriority(ctx, shippingOrder.ShopInfo().ShopID)
	itemSplitter := organizer.NewRegionItemSplitter(regionPriorityList)
	regionToItems, err := itemSplitter.SplitItems(ctx, shippingOrder.Items)
	if err != nil {
		return nil, err
	}

	var splitOrders []group_entity.ShippingOrder
	for region, items := range regionToItems {
		shopeeItems, sellerItems, err := r.splitItemsByFulfilmentType(ctx, items, region, regionPriorityList)
		if err != nil {
			return nil, err
		}

		if len(shopeeItems) > 0 {
			shopeeShippingOrders, err := r.organizerShopeeItems(ctx, buyerInfo, shippingOrder, shopeeItems, region, regionPriorityList)
			if err != nil {
				return nil, err
			}
			splitOrders = append(splitOrders, shopeeShippingOrders...)
		}
		if len(sellerItems) > 0 {
			sellerShippingOrders, err := r.organizerSellerItems(ctx, buyerInfo, shippingOrder, sellerItems, region, regionPriorityList)
			if err != nil {
				return nil, err
			}
			splitOrders = append(splitOrders, sellerShippingOrders...)
		}
	}

	return splitOrders, nil
}

func (r *ResellShippingOrderProcessor) getRegionPriority(ctx context.Context, shopID uint64) []string {
	localSIPInfo, err := r.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get local SIP shop info error, shop_id: %d, err: %v", shopID, err)
	}
	regionPriority := []string{
		envvar.GetCID(ctx),
	}
	if localSIPInfo.SIPPrimaryRegion != "" {
		regionPriority = append(regionPriority, strings.ToUpper(localSIPInfo.SIPPrimaryRegion))
	}
	return regionPriority
}

func (r *ResellShippingOrderProcessor) splitItemsByFulfilmentType(
	ctx context.Context,
	items []group_entity.ShippingOrderItem,
	region string,
	regionPriorityList []string,
) ([]group_entity.ShippingOrderItem, []group_entity.ShippingOrderItem, error) {
	itemSplitter := organizer.NewFulfilmentTypeItemSplitter(
		[]constant.FulfilmentType{constant.FulfilmentTypeShopee, constant.FulfilmentTypeSeller},
		func(locations group_entity.StockLocations) group_entity.StockLocations {
			return r.filterLocationsByRegion(locations, region, regionPriorityList)
		},
	)
	fulfilmentTypeToItems, err := itemSplitter.SplitItems(ctx, items)
	if err != nil {
		return nil, nil, err
	}
	return fulfilmentTypeToItems[constant.FulfilmentTypeShopee], fulfilmentTypeToItems[constant.FulfilmentTypeSeller], nil
}

func (r *ResellShippingOrderProcessor) organizerShopeeItems(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	items []group_entity.ShippingOrderItem,
	region string,
	regionPriorityList []string,
) ([]group_entity.ShippingOrder, error) {
	keyToItems, err := r.itemOrganizer.OrganizeItems(
		ctx,
		items,
		r.shopeeSourceSelect(region, regionPriorityList),
		r.shopeeSourcePrioritize(ctx, buyerInfo.Address),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == constant.FulfilmentTypeShopee {
			address, err := r.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = address.AddressID
		}
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (r *ResellShippingOrderProcessor) organizerSellerItems(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shippingOrder group_entity.ShippingOrder,
	items []group_entity.ShippingOrderItem,
	region string,
	regionPriorityList []string,
) ([]group_entity.ShippingOrder, error) {
	keyToItems, err := r.itemOrganizer.OrganizeItems(
		ctx,
		items,
		r.sellerSourceSelect(region, regionPriorityList),
		r.sellerSourcePrioritize(ctx, buyerInfo, shippingOrder.ShopInfo()),
		constant.ItemOrganizeStrategyMinimumParcel,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	return organizer.SplitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (r *ResellShippingOrderProcessor) shopeeSourceSelect(region string, regionPriorityList []string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, error) {
		return r.filterLocationsByRegion(item.ShopeeStocks(), region, regionPriorityList), nil
	}
}

func (r *ResellShippingOrderProcessor) shopeeSourcePrioritize(
	ctx context.Context,
	buyerAddress entity.ItemGroupBuyerAddress,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		return r.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
	}
}

func (r *ResellShippingOrderProcessor) sellerSourceSelect(region string, regionPriorityList []string) organizer.ItemOrganizeSourceSelectFunc {
	return func(item group_entity.ShippingOrderItem) (group_entity.StockLocations, error) {
		sellerStocks := r.filterLocationsByRegion(item.SellerStocks(), region, regionPriorityList)
		return sellerStocks, nil
	}
}

func (r *ResellShippingOrderProcessor) sellerSourcePrioritize(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopInfo group_entity.ShopInfo,
) organizer.ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		buyerAddressGeo, err := r.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		return r.warehousePriorityService.PrioritizeSellerMultiWarehouse(
			ctx,
			buyerAddressGeo,
			shopInfo.SellerID,
			shopInfo.Warehouses,
			sources,
		), nil
	}
}

// only need the buyer address Geo for multi-warehouse shop
// for SIP affiliated shop, we need the dummy buyer Geo instead of real buyer Geo
func (r *ResellShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo group_entity.BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, error) {
	localSIPInfo, err := r.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		logger.CtxLogErrorf(ctx, "get local SIP shop info error, shop_id: %d, err: %v", shopID, err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = r.addressService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = r.addressService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (r *ResellShippingOrderProcessor) filterLocationsByRegion(locations group_entity.StockLocations, region string, regionPriorityList []string) group_entity.StockLocations {
	var filteredLocations group_entity.StockLocations
	for _, location := range locations {
		sourceRegion := location.Region()
		if region == "" && !slices.Contains(regionPriorityList, sourceRegion) {
			filteredLocations = append(filteredLocations, location)
			continue
		}

		if sourceRegion == region {
			filteredLocations = append(filteredLocations, location)
		}
	}
	return filteredLocations
}
