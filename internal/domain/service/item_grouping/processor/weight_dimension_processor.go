// Package item_grouping 提供订单商品分组相关的领域服务
// 本包实现了基于重量和尺寸限制的订单拆分逻辑，确保每个拆分后的订单都符合物流渠道的承载能力
package processor

import (
	"context"
	"fmt"
	"math"
	"slices"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/channel_generator"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_info"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/logistics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// WeightDimensionProcessor 重量尺寸处理器
// 负责根据物流渠道的重量和尺寸限制对订单进行智能拆分，确保每个拆分后的订单都能通过物流渠道的承载能力检查
// 主要功能包括：
// 1. 获取订单中所有商品的尺寸重量信息
// 2. 查询可用物流渠道及其重量尺寸限制
// 3. 根据限制条件智能拆分订单
// 4. 支持多种重量计算公式（实重、体积重等）
type WeightDimensionProcessor struct {
	configAccessor         config.ConfAccessor                       // 配置访问器，用于获取系统配置参数
	channelGenerateService channel_generator.ChannelGeneratorService // 渠道生成服务，用于获取可用的物流渠道
	itemService            item_info.ItemService                     // 商品信息服务，用于获取商品的尺寸重量信息
	logisticsService       logistics.LogisticsService                // 物流服务，用于查询渠道的重量尺寸限制
}

// NewWeightDimensionProcessor 创建新的重量尺寸处理器实例
// 参数:
//   - configAccessor: 配置访问器
//   - channelGenerateService: 渠道生成服务
//   - itemService: 商品信息服务
//   - logisticsService: 物流服务
//
// 返回: WeightDimensionProcessor实例指针
func NewWeightDimensionProcessor(
	configAccessor config.ConfAccessor,
	channelGenerateService channel_generator.ChannelGeneratorService,
	itemService item_info.ItemService,
	logisticsService logistics.LogisticsService,
) *WeightDimensionProcessor {
	return &WeightDimensionProcessor{
		configAccessor:         configAccessor,
		channelGenerateService: channelGenerateService,
		itemService:            itemService,
		logisticsService:       logisticsService,
	}
}

// Name 返回处理器名称
// 返回: 重量尺寸处理器的标识名称
func (w *WeightDimensionProcessor) Name() ShippingOrderProcessorName {
	return WeightDimensionProcessorName
}

// IsAbleToProcess 判断是否能够处理指定的订单
// 处理条件：
// 1. 订单不是单商品单数量的订单（除非是预售券卖家）
// 2. 订单已经选择了履约来源
// 参数:
//   - ctx: 上下文
//   - shippingOrder: 待处理的订单
//
// 返回: 是否能够处理该订单
func (w *WeightDimensionProcessor) IsAbleToProcess(ctx context.Context, buyerInfo group_entity.BuyerInfo, shippingOrder group_entity.ShippingOrder) bool {
	// 单商品单数量订单无需拆分（预售券卖家除外）
	if len(shippingOrder.Items) == 1 &&
		(shippingOrder.Items[0].Quantity == 1 || shippingOrder.Items[0].ShopInfo.IsPreSaleVoucherSeller) {
		return false
	}

	// 必须已选择履约来源
	return shippingOrder.IsSourceSelected()
}

// DoProcess 执行订单处理逻辑
// 这是处理器的主入口方法，负责协调整个重量尺寸拆分流程
// 参数:
//   - ctx: 上下文
//   - _: 买家信息（当前未使用）
//   - shippingOrders: 待处理的订单列表
//
// 返回: 处理后的订单列表和可能的错误
func (w *WeightDimensionProcessor) DoProcess(ctx context.Context, _ group_entity.BuyerInfo, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, error) {
	// TODO: 添加性能监控
	//fs := metrics.StartFunctionSpan("itemgrouping.WeightDimensionProcessor.DoProcess")
	//defer fs.Finish(ctx)

	processedShippingOrders, err := w.process(ctx, shippingOrders)
	if err != nil {
		// 处理失败时记录日志并返回原订单（降级处理）
		logger.CtxLogInfof(ctx, "weight dimension processor skipped, err: %v", err)
		return shippingOrders, nil
	}

	return processedShippingOrders, nil
}

// process 核心处理逻辑
// 执行完整的重量尺寸处理流程：
// 1. 计算订单的可用渠道
// 2. 获取商品尺寸重量信息
// 3. 查询渠道重量尺寸限制
// 4. 根据限制拆分订单
// 参数:
//   - ctx: 上下文
//   - shippingOrders: 待处理的订单列表
//
// 返回: 处理后的订单列表和可能的错误
func (w *WeightDimensionProcessor) process(ctx context.Context, shippingOrders []group_entity.ShippingOrder) ([]group_entity.ShippingOrder, error) {
	// 步骤1: 计算每个订单的可用渠道
	withEnableChannelShippingOrders, err := w.calculateShippingOrderEnabledChannels(ctx, shippingOrders)
	if err != nil {
		return nil, err
	}

	// 步骤2: 获取所有商品的尺寸重量信息
	itemIDToModelIDToSizeInfo, err := w.getShippingOrderItemModelSizeInfo(ctx, shippingOrders)
	if err != nil {
		return nil, err
	}

	// 步骤3: 查询渠道的重量尺寸限制信息
	withWeighDimensionInfoShippingOrders, err := w.getShippingOrderWeighDimensionInfo(
		ctx,
		withEnableChannelShippingOrders,
		itemIDToModelIDToSizeInfo,
	)
	if err != nil {
		return nil, err
	}

	// 步骤4: 根据重量尺寸限制拆分订单
	processedShippingOrders, err := w.processByWeighDimension(ctx, withWeighDimensionInfoShippingOrders, itemIDToModelIDToSizeInfo)
	if err != nil {
		return nil, err
	}

	return processedShippingOrders, nil
}

// calculateShippingOrderEnabledChannels 计算订单的可用渠道
// 根据订单类型（Choice、TradeIn或普通订单）获取相应的可用渠道列表
// 参数:
//   - ctx: 上下文
//   - shippingOrders: 订单列表
//
// 返回: 带有可用渠道信息的订单列表和可能的错误
func (w *WeightDimensionProcessor) calculateShippingOrderEnabledChannels(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
) ([]group_entity.ShippingOrderWithEnabledChannels, error) {
	var withEnabledChannelShippingOrders []group_entity.ShippingOrderWithEnabledChannels

	for _, shippingOrder := range shippingOrders {
		// todo 目前没有入参数，需要找 MP
		if shippingOrder.IsChoiceEligible && len(shippingOrder.EnabledChoiceChannels) > 0 {
			withEnabledChannelShippingOrders = append(withEnabledChannelShippingOrders, group_entity.ShippingOrderWithEnabledChannels{
				ShippingOrder:   shippingOrder,
				EnabledChannels: collection.NewSetFromSlice(shippingOrder.EnabledChoiceChannels),
			})
			continue
		} else if shippingOrder.IsTradeInEligible && len(shippingOrder.EnabledTradeInChannels) > 0 {
			withEnabledChannelShippingOrders = append(withEnabledChannelShippingOrders, group_entity.ShippingOrderWithEnabledChannels{
				ShippingOrder:   shippingOrder,
				EnabledChannels: collection.NewSetFromSlice(shippingOrder.EnabledTradeInChannels),
			})
			continue
		}

		// 普通订单：动态生成可用渠道
		itemIDToChannelIDSet, err := w.channelGenerateService.GenerateItemsToEnabledChannels(
			ctx,
			group_entity.GetShippingOrderShopIDToItemIDs(shippingOrder),
			entity.FulfillmentInfo{
				ManagedBySBS:  shippingOrder.IsManageBySBS(),
				WarehouseCode: group_entity.GetShippingOrderFulfilmentSource(shippingOrder, shippingOrder.FulfilmentInfo),
			},
		)
		if err != nil {
			return nil, err
		}

		// 计算所有商品共同支持的渠道（取交集）
		enabledChannelSet := collection.IntersectSets(collection.MapToSlice(itemIDToChannelIDSet))
		withEnabledChannelShippingOrders = append(withEnabledChannelShippingOrders, group_entity.ShippingOrderWithEnabledChannels{
			ShippingOrder:   shippingOrder,
			EnabledChannels: enabledChannelSet,
		})
	}

	return withEnabledChannelShippingOrders, nil
}

// getShippingOrderItemModelSizeInfo 获取订单中所有商品的尺寸重量信息
// 批量查询商品信息，构建商品ID->型号ID->尺寸信息的映射关系
// 参数:
//   - ctx: 上下文
//   - shippingOrders: 订单列表
//
// 返回: 商品尺寸信息映射和可能的错误
func (w *WeightDimensionProcessor) getShippingOrderItemModelSizeInfo(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrder,
) (map[uint64]map[uint64]entity.ItemSizeInfo, error) {
	// 收集所有需要查询的商品
	shopItemIDSet := collection.NewSet[entity.ShopItemID]()
	for _, shippingOrder := range shippingOrders {
		shippingOrder.ForEachAllItems(func(item group_entity.ShippingOrderItem) {
			shopItemIDSet.Add(entity.ShopItemID{ShopID: item.GetShopInfo().ShopID, ItemID: item.ItemID})
		})
	}

	// 批量查询商品信息
	shopItemIDToInfo, err := w.itemService.BatchItemInfo(ctx, shopItemIDSet.ToSlice())
	if err != nil {
		return nil, err
	}

	// 构建商品尺寸信息映射
	itemIDToModelIDToSizeInfo := make(map[uint64]map[uint64]entity.ItemSizeInfo)
	for _, shippingOrder := range shippingOrders {
		shippingOrder.ForEachAllItems(func(item group_entity.ShippingOrderItem) {
			shopItemID := entity.ShopItemID{ShopID: item.GetShopInfo().ShopID, ItemID: item.ItemID}
			if itemIDToModelIDToSizeInfo[item.ItemID] == nil {
				itemIDToModelIDToSizeInfo[item.ItemID] = make(map[uint64]entity.ItemSizeInfo)
			}
			// 对尺寸进行排序（长>=宽>=高）
			modelSizeInfo := shopItemIDToInfo[shopItemID].ModelIDToSizeInfo[item.ModelID]
			itemIDToModelIDToSizeInfo[item.ItemID][item.ModelID] = w.sortDimension(modelSizeInfo)
		})
	}

	return itemIDToModelIDToSizeInfo, nil
}

// sortDimension 对商品尺寸进行排序
// 将长宽高按照从大到小的顺序排列（长>=宽>=高），便于后续的体积计算
// 参数:
//   - i: 原始尺寸信息
//
// 返回: 排序后的尺寸信息
func (w *WeightDimensionProcessor) sortDimension(i entity.ItemSizeInfo) entity.ItemSizeInfo {
	dimensions := []uint64{i.Length, i.Width, i.Height}
	slices.Sort(dimensions)
	return entity.ItemSizeInfo{
		Weight: i.Weight,
		Length: dimensions[2], // 最大值作为长
		Width:  dimensions[1], // 中间值作为宽
		Height: dimensions[0], // 最小值作为高
	}
}

// getShippingOrderWeighDimensionInfo 获取订单在各个渠道的重量尺寸限制信息
// 为每个订单的每个可用渠道查询重量尺寸限制，用于后续的拆分决策
// 参数:
//   - ctx: 上下文
//   - shippingOrders: 带有可用渠道的订单列表
//   - itemIDToModelIDToSizeInfo: 商品尺寸信息映射
//
// 返回: 带有渠道重量尺寸信息的订单列表和可能的错误
func (w *WeightDimensionProcessor) getShippingOrderWeighDimensionInfo(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrderWithEnabledChannels,
	itemIDToModelIDToSizeInfo map[uint64]map[uint64]entity.ItemSizeInfo,
) ([]group_entity.ShippingOrderWithChannelWeightDimensionInfo, error) {
	// 构建查询请求
	uniqueIDToIdx := make(map[string]int)
	var queries []lpslib.OrderSplitLogisticsQuery
	for idx, shippingOrder := range shippingOrders {
		// 构建订单商品信息
		items := make([]lpslib.OrderSplitChannelLogisticsItem, 0, len(shippingOrder.Items))
		shippingOrder.ForEachAllItems(func(item group_entity.ShippingOrderItem) {
			sizeInfo := itemIDToModelIDToSizeInfo[item.ItemID][item.ModelID]
			items = append(items, lpslib.OrderSplitChannelLogisticsItem{
				ItemID:   int(item.ItemID),
				ModelID:  int(item.ModelID),
				Quantity: int(item.Quantity),
				Weight:   float64(sizeInfo.Weight),
				Length:   float64(sizeInfo.Length),
				Width:    float64(sizeInfo.Width),
				Height:   float64(sizeInfo.Height),
			})
		})

		// 为每个可用渠道创建查询
		for _, channelID := range shippingOrder.EnabledChannels.ToSlice() {
			uniqueID := fmt.Sprintf("%d-%d", channelID, idx)
			uniqueIDToIdx[uniqueID] = idx
			queries = append(queries, lpslib.OrderSplitLogisticsQuery{
				UniqueID:  uniqueID,
				ChannelID: channelID,
				Items:     items,
			})
		}
	}

	// 批量查询渠道重量尺寸限制
	uniqueIDToWeightDimensionInfo, err := w.logisticsService.GetChannelWeightDimensionInfo(ctx, queries)
	if err != nil {
		return nil, err
	}

	// 重新组织查询结果
	idxToChannelIDToWeightDimensionInfo := make(map[int]map[int]lpslib.ChannelWeightDimensionInfo)
	for uniqueID, info := range uniqueIDToWeightDimensionInfo {
		idx := uniqueIDToIdx[uniqueID]
		if idxToChannelIDToWeightDimensionInfo[idx] == nil {
			idxToChannelIDToWeightDimensionInfo[idx] = make(map[int]lpslib.ChannelWeightDimensionInfo)
		}
		idxToChannelIDToWeightDimensionInfo[idx][info.ChannelID] = info
	}

	// 构建最终结果
	var withChannelWeightDimensionInfoShippingOrders []group_entity.ShippingOrderWithChannelWeightDimensionInfo
	for idx, shippingOrder := range shippingOrders {
		withChannelWeightDimensionInfoShippingOrders = append(
			withChannelWeightDimensionInfoShippingOrders,
			group_entity.ShippingOrderWithChannelWeightDimensionInfo{
				ShippingOrder:                  shippingOrder.ShippingOrder,
				ChannelIDToWeightDimensionInfo: idxToChannelIDToWeightDimensionInfo[idx],
			},
		)
	}

	return withChannelWeightDimensionInfoShippingOrders, nil
}

// processByWeighDimension 根据重量尺寸限制处理订单
// 对每个订单根据其渠道的重量尺寸限制进行拆分处理
// 参数:
//   - ctx: 上下文
//   - shippingOrders: 带有渠道重量尺寸信息的订单列表
//   - itemIDToModelIDToSizeInfo: 商品尺寸信息映射
//
// 返回: 处理后的订单列表和可能的错误
func (w *WeightDimensionProcessor) processByWeighDimension(
	ctx context.Context,
	shippingOrders []group_entity.ShippingOrderWithChannelWeightDimensionInfo,
	itemIDToModelIDToSizeInfo map[uint64]map[uint64]entity.ItemSizeInfo,
) ([]group_entity.ShippingOrder, error) {
	var processedShippingOrders []group_entity.ShippingOrder
	for _, shippingOrder := range shippingOrders {
		// 根据重量尺寸限制拆分订单
		splitShippingOrders, err := w.splitByWeighDimension(
			shippingOrder.ShippingOrder,
			shippingOrder.ChannelIDToWeightDimensionInfo,
			itemIDToModelIDToSizeInfo,
		)
		if err != nil {
			return nil, err
		}
		// TODO: 添加拆分指标统计
		//w.generateMetrics(ctx, len(splitShippingOrders))
		processedShippingOrders = append(processedShippingOrders, splitShippingOrders...)
	}
	return processedShippingOrders, nil
}

// splitByWeighDimension 根据重量尺寸限制拆分单个订单
// 核心拆分逻辑：
// 1. 检查是否有渠道能够承载整个订单（无需拆分）
// 2. 收集所有渠道的限制条件
// 3. 选择最优的拆分方案（拆分数量最少）
// 参数:
//   - shippingOrder: 待拆分的订单
//   - channelIDToWeightDimensionInfo: 渠道重量尺寸信息映射
//   - itemIDToModelIDToSizeInfo: 商品尺寸信息映射
//
// 返回: 拆分后的订单列表和可能的错误
func (w *WeightDimensionProcessor) splitByWeighDimension(
	shippingOrder group_entity.ShippingOrder,
	channelIDToWeightDimensionInfo map[int]lpslib.ChannelWeightDimensionInfo,
	itemIDToModelIDToSizeInfo map[uint64]map[uint64]entity.ItemSizeInfo,
) ([]group_entity.ShippingOrder, error) {
	// 空订单直接返回
	if len(shippingOrder.Items) == 0 {
		return []group_entity.ShippingOrder{shippingOrder}, nil
	}

	var channelIDs []int
	channelIDToLimitation := make(map[int]group_entity.WeighDimensionLimitation)
	for _, info := range channelIDToWeightDimensionInfo {
		if info.RetCode == 0 {
			// 如果有任何渠道通过了重量尺寸检查，则无需拆分
			return []group_entity.ShippingOrder{shippingOrder}, nil
		}
		// 解析渠道的重量尺寸限制
		var limitation group_entity.WeighDimensionLimitation
		for _, validation := range info.WeightDimensionValidation {
			switch validation.RuleType {
			case lpslib.WeightDimensionRuleTypeWeight:
				if validation.ByVolumetricWeight {
					limitation.VolumetricFactor = validation.VolumetricFactor
				}
				limitation.Formula = validation.Formula
				limitation.WeightLimitationMax = validation.LimitationValueMax
			case lpslib.WeightDimensionRuleTypeHeight:
				limitation.HeightLimitationMax = validation.LimitationValueMax
			}
		}
		channelIDs = append(channelIDs, info.ChannelID)
		channelIDToLimitation[info.ChannelID] = limitation
	}
	if len(channelIDs) == 0 {
		return nil, fsserr.ErrWeightAndDimensionLimit
	}

	// 按渠道ID排序，确保结果的一致性
	sort.Slice(channelIDs, func(i, j int) bool {
		return channelIDs[i] < channelIDs[j]
	})

	// 尝试不同渠道的限制条件，选择拆分数量最少的方案
	withSizeInfoItems := w.buildSizeInfoItems(shippingOrder.Items, itemIDToModelIDToSizeInfo)
	maxShippingOrder := w.configAccessor.GetWeightDimMaxNumOfShippingOrders(context.Background())
	splitItems := w.splitItems(withSizeInfoItems, channelIDToLimitation[channelIDs[0]], maxShippingOrder)
	for _, channelID := range channelIDs[1:] {
		newSplitItems := w.splitItems(withSizeInfoItems, channelIDToLimitation[channelID], maxShippingOrder)
		if len(newSplitItems) == 0 {
			continue
		}
		// 选择拆分数量更少的方案
		if len(newSplitItems) < len(splitItems) || len(splitItems) == 0 {
			splitItems = newSplitItems
		}
	}
	if len(splitItems) == 0 {
		return nil, fsserr.ErrWeightAndDimensionLimit
	}

	// 构建拆分后的订单
	var splitShippingOrders []group_entity.ShippingOrder
	for _, items := range splitItems {
		splitShippingOrders = append(splitShippingOrders, shippingOrder.SplitNew(items))
	}
	return splitShippingOrders, nil
}

// buildSizeInfoItems 构建带有尺寸信息的商品列表
// 为每个商品附加其尺寸重量信息，支持套装商品的尺寸计算
// 参数:
//   - items: 订单商品列表
//   - itemIDToModelIDToSizeInfo: 商品尺寸信息映射
//
// 返回: 带有尺寸信息的商品列表
func (w *WeightDimensionProcessor) buildSizeInfoItems(
	items []group_entity.ShippingOrderItem,
	itemIDToModelIDToSizeInfo map[uint64]map[uint64]entity.ItemSizeInfo,
) []group_entity.ShippingOrderItemWithSizeInfo {
	var withSizeInfoItems []group_entity.ShippingOrderItemWithSizeInfo
	for _, item := range items {
		var sizeInfo entity.ItemSizeInfo
		if len(item.PackageItems) == 0 {
			// 普通商品：直接使用商品的尺寸信息
			sizeInfo = itemIDToModelIDToSizeInfo[item.ItemID][item.ModelID]
			sizeInfo.DimensionProduct = sizeInfo.Length * sizeInfo.Width * sizeInfo.Height
		} else {
			// 套装商品：累加所有子商品的尺寸重量
			for _, packageItem := range item.PackageItems {
				packageItemSizeInfo := itemIDToModelIDToSizeInfo[packageItem.ItemID][packageItem.ModelID]
				quantity := uint64(packageItem.Quantity)
				weight := packageItemSizeInfo.Weight
				length, width, height := packageItemSizeInfo.Length, packageItemSizeInfo.Width, packageItemSizeInfo.Height
				sizeInfo.Weight += weight * quantity
				sizeInfo.Length = max(sizeInfo.Length, length) // 长度取最大值
				sizeInfo.Width = max(sizeInfo.Width, width)    // 宽度取最大值
				sizeInfo.Height += height * quantity           // 高度累加
				sizeInfo.DimensionProduct += quantity * (length * width * height)
			}
		}

		withSizeInfoItems = append(withSizeInfoItems, group_entity.ShippingOrderItemWithSizeInfo{
			ShippingOrderItem: item,
			SizeInfo:          sizeInfo,
		})
	}
	return withSizeInfoItems
}

// splitItems 拆分商品列表
// 当前使用基础公式进行拆分，未来可扩展支持更多拆分算法
// 参数:
//   - items: 带有尺寸信息的商品列表
//   - limitation: 重量尺寸限制
//   - maxShippingOrders: 最大拆分订单数
//
// 返回: 拆分后的商品组列表
func (w *WeightDimensionProcessor) splitItems(
	items []group_entity.ShippingOrderItemWithSizeInfo,
	limitation group_entity.WeighDimensionLimitation,
	maxShippingOrders int,
) [][]group_entity.ShippingOrderItem {
	return w.splitItemsByBasicFormula(items, limitation, maxShippingOrders)
}

// splitItemsByBasicFormula 使用基础公式拆分商品
// 核心拆分算法：
// 1. 验证限制条件的有效性
// 2. 按商品优先级排序
// 3. 贪心算法逐个添加商品到当前组
// 4. 超出限制时创建新组
// 参数:
//   - items: 带有尺寸信息的商品列表
//   - limitation: 重量尺寸限制
//   - maxShippingOrders: 最大拆分订单数
//
// 返回: 拆分后的商品组列表，失败时返回nil
func (w *WeightDimensionProcessor) splitItemsByBasicFormula(
	items []group_entity.ShippingOrderItemWithSizeInfo,
	limitation group_entity.WeighDimensionLimitation,
	maxShippingOrders int,
) [][]group_entity.ShippingOrderItem {
	// 验证限制条件
	if !w.validateRule(limitation) {
		return w.wrapItemsWithoutSplit(items)
	}

	// 按商品优先级排序（确保拆分结果的一致性）
	sort.Slice(items, func(i, j int) bool {
		return items[i].Less(items[j].ShippingOrderItem)
	})

	var splitItems [][]group_entity.ShippingOrderItem
	var currentItems []group_entity.ShippingOrderItem
	var currentWeight, currentHeight, currentChargeableWeight, maxLength, maxWidth int
	weightLimit, heightLimit := w.getWeightHeightLimit(limitation)

	for _, item := range items {
		itemWeight := int(item.SizeInfo.Weight)
		itemHeight := int(item.SizeInfo.Height)
		// 单个商品超出限制，无法拆分
		if itemWeight > weightLimit || itemHeight > heightLimit {
			return nil
		}

		itemMaxLength := max(int(item.SizeInfo.Length), maxLength)
		itemMaxWidth := max(int(item.SizeInfo.Width), maxWidth)
		currentVolumetricWeight, itemVolumetricWeight := w.calculateItemWeights(item, limitation, itemMaxLength, itemMaxWidth, currentHeight)
		if itemVolumetricWeight > weightLimit {
			return nil
		}

		// 处理商品数量，可能需要拆分到多个组
		remainingQuantity := int(item.Quantity)
		for remainingQuantity > 0 {
			// 计算当前组能容纳的数量
			allowableQuantity := w.calculateAllowableQuantity(
				limitation.Formula,
				weightLimit,
				heightLimit,
				currentWeight,
				currentHeight,
				currentVolumetricWeight,
				currentChargeableWeight,
				itemWeight,
				itemHeight,
				itemVolumetricWeight,
				remainingQuantity,
			)

			if allowableQuantity > 0 {
				// 添加到当前组
				currentItem := item
				currentItem.Quantity = uint32(allowableQuantity)
				currentItems = append(currentItems, currentItem.ShippingOrderItem)
				currentWeight += allowableQuantity * itemWeight
				currentChargeableWeight += allowableQuantity * max(itemVolumetricWeight, itemWeight)
				currentHeight += allowableQuantity * itemHeight
				maxLength, maxWidth = itemMaxLength, itemMaxWidth
				remainingQuantity -= allowableQuantity
			}

			// 当前组已满或还有剩余数量，创建新组
			if currentWeight >= weightLimit || currentHeight >= heightLimit || remainingQuantity > 0 {
				splitItems = append(splitItems, currentItems)
				currentItems = []group_entity.ShippingOrderItem{}
				currentWeight, currentHeight, currentChargeableWeight, maxLength, maxWidth = 0, 0, 0, 0, 0
				if len(splitItems) > maxShippingOrders {
					return nil
				}
			}
		}
	}

	// 处理最后一组
	if len(currentItems) > 0 {
		splitItems = append(splitItems, currentItems)
	}
	if len(splitItems) > maxShippingOrders {
		return nil
	}
	return splitItems
}

// validateRule 验证重量尺寸限制规则的有效性
// 检查限制条件是否合理，确保拆分算法能够正常工作
// 参数:
//   - limit: 重量尺寸限制
//
// 返回: 规则是否有效
func (w *WeightDimensionProcessor) validateRule(limit group_entity.WeighDimensionLimitation) bool {
	// 必须至少有重量或高度限制
	if limit.WeightLimitationMax <= 0 && limit.HeightLimitationMax <= 0 {
		return false
	}
	// 体积重公式需要有效的体积重系数
	switch limit.Formula {
	case group_entity.WeightFormula3002, group_entity.WeightFormula3016, group_entity.WeightFormula3017, group_entity.WeightFormula304:
		if limit.VolumetricFactor <= 0 {
			return false
		}
	default:
		return true
	}
	return true
}

// wrapItemsWithoutSplit 将商品包装为单个组（不拆分）
// 当限制条件无效或不需要拆分时使用
// 参数:
//   - items: 带有尺寸信息的商品列表
//
// 返回: 包含单个组的商品列表
func (w *WeightDimensionProcessor) wrapItemsWithoutSplit(items []group_entity.ShippingOrderItemWithSizeInfo) [][]group_entity.ShippingOrderItem {
	var splitItems []group_entity.ShippingOrderItem
	for _, item := range items {
		splitItems = append(splitItems, item.ShippingOrderItem)
	}
	return [][]group_entity.ShippingOrderItem{splitItems}
}

// getWeightHeightLimit 获取有效的重量和高度限制
// 将无效的限制值（<=0）转换为最大值，避免计算错误
// 参数:
//   - limitation: 重量尺寸限制
//
// 返回: 有效的重量限制和高度限制
func (w *WeightDimensionProcessor) getWeightHeightLimit(limitation group_entity.WeighDimensionLimitation) (int, int) {
	weightLimit := limitation.WeightLimitationMax
	heightLimit := limitation.HeightLimitationMax
	if weightLimit <= 0 {
		weightLimit = math.MaxInt
	}
	if heightLimit <= 0 {
		heightLimit = math.MaxInt
	}
	return weightLimit, heightLimit
}

// calculateItemWeights 计算商品的重量信息
// 根据不同的重量计算公式，计算当前体积重和单个商品的体积重
// 支持多种重量计算公式：3002、3016、3017、304等
// 参数:
//   - item: 带有尺寸信息的商品
//   - limitation: 重量尺寸限制
//   - itemMaxLength: 商品最大长度
//   - itemMaxWidth: 商品最大宽度
//   - currentHeight: 当前累计高度
//
// 返回: 当前体积重和单个商品体积重
func (w *WeightDimensionProcessor) calculateItemWeights(
	item group_entity.ShippingOrderItemWithSizeInfo,
	limitation group_entity.WeighDimensionLimitation,
	itemMaxLength, itemMaxWidth, currentHeight int,
) (int, int) {
	var currentVolumetricWeight, itemVolumetricWeight int
	switch limitation.Formula {
	case group_entity.WeightFormula3002, group_entity.WeightFormula3017:
		// 公式3002和3017：基于长×宽×高计算体积重
		// 注意：公式3017的阈值(5kg)通常小于渠道重量限制(30kg)，所以等同于3002
		currentVolumetricWeight = w.calculateVolumetricWeight(itemMaxLength*itemMaxWidth*currentHeight, limitation.VolumetricFactor)
		itemVolumetricWeight = w.calculateVolumetricWeight(itemMaxLength*itemMaxWidth*int(item.SizeInfo.Height), limitation.VolumetricFactor)
	case group_entity.WeightFormula3016, group_entity.WeightFormula304:
		// 公式3016和304：基于商品自身体积计算体积重
		// 注意：3016的计算方法在某些情况下可能导致allowableQuantity偏小
		currentVolumetricWeight = 0
		itemVolumetricWeight = w.calculateVolumetricWeight(int(item.SizeInfo.DimensionProduct), limitation.VolumetricFactor)
	default:
		// 其他公式：不计算体积重
		currentVolumetricWeight, itemVolumetricWeight = 0, 0
	}
	return currentVolumetricWeight, itemVolumetricWeight
}

// calculateVolumetricWeight 计算体积重
// 体积重 = 体积 × 膨胀系数 / 体积重系数
// 参数:
//   - dimensionProduct: 体积（长×宽×高）
//   - factor: 体积重系数
//
// 返回: 计算得到的体积重
func (w *WeightDimensionProcessor) calculateVolumetricWeight(dimensionProduct, factor int) int {
	if factor == 0 {
		return 0
	}
	return dimensionProduct * group_entity.VolumetricWeightInflation / factor
}

// calculateAllowableQuantity 计算当前组能容纳的商品数量
// 综合考虑重量限制、高度限制和体积重限制，计算最大可容纳数量
// 参数:
//   - formula: 重量计算公式
//   - weightLimit: 重量限制
//   - heightLimit: 高度限制
//   - currentWeight: 当前重量
//   - currentHeight: 当前高度
//   - currentVolumetricWeight: 当前体积重
//   - currentChargeableWeight: 当前计费重量
//   - itemWeight: 单个商品重量
//   - itemHeight: 单个商品高度
//   - itemVolumetricWeight: 单个商品体积重
//   - remainingQuantity: 剩余数量
//
// 返回: 可容纳的数量
func (w *WeightDimensionProcessor) calculateAllowableQuantity(
	formula int,
	weightLimit int,
	heightLimit int,
	currentWeight int,
	currentHeight int,
	currentVolumetricWeight int,
	currentChargeableWeight int,
	itemWeight int,
	itemHeight int,
	itemVolumetricWeight int,
	remainingQuantity int,
) int {
	allowableByWeight := remainingQuantity
	allowableByHeight := remainingQuantity
	allowableByVolumetric := remainingQuantity

	// 根据重量限制计算可容纳数量
	if itemWeight > 0 {
		if formula == group_entity.WeightFormula304 || formula == group_entity.WeightFormula3016 {
			// 使用计费重量（实重和体积重的较大值）
			itemChargeableWeight := max(itemWeight, itemVolumetricWeight)
			allowableByWeight = (weightLimit - currentChargeableWeight) / itemChargeableWeight
		} else {
			// 使用实重
			allowableByWeight = (weightLimit - currentWeight) / itemWeight
		}
	}
	// 根据高度限制计算可容纳数量
	if itemHeight > 0 {
		allowableByHeight = (heightLimit - currentHeight) / itemHeight
	}
	// 根据体积重限制计算可容纳数量
	if itemVolumetricWeight > 0 {
		allowableByVolumetric = (weightLimit - currentVolumetricWeight) / itemVolumetricWeight
	}

	// 取最小值作为最终可容纳数量
	return min(allowableByWeight, allowableByHeight, allowableByVolumetric, remainingQuantity)
}
