package item_grouping

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/group_entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping/processor"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

// ItemGrouper 定义商品分组操作的接口。
// 它提供了基于业务规则、买家信息和各种约束条件对配送订单进行分组的方法。
type ItemGrouper interface {
	// GroupItems 根据指定的规则和约束条件对配送订单进行分组。
	//
	// 参数:
	//   - ctx: 请求生命周期管理的上下文
	//   - shippingOrders: 待分组的配送订单列表
	//   - buyerInfo: 买家信息
	//   - rules: 要应用的分组规则（捆绑、隔离、渠道、约束规则）
	//   - skipSplitByWeightDimension: 是否跳过基于重量/尺寸的拆分
	//
	// 返回:
	//   - []group_entity.ShippingOrder: 分组后的配送订单
	//   - fsserr.Error: 分组失败时的错误
	GroupItems(ctx context.Context, shippingOrders []group_entity.ShippingOrder, buyerInfo group_entity.BuyerInfo, rules *group_entity.GroupingRules, skipSplitByWeightDimension bool) ([]group_entity.ShippingOrder, fsserr.Error)
}

// DefaultItemGrouper 是 ItemGrouper 接口的默认实现。
// 它协调各种处理器并应用业务规则来有效地对商品进行分组。
type DefaultItemGrouper struct {
	configAccessor config.ConfAccessor

	advanceBookingShippingOrderProcessor *processor.AdvanceBookingShippingOrderProcessor
	cbLFFShippingOrderProcessor          *processor.CBLFFShippingOrderProcessor
	resellShippingOrderProcessor         *processor.ResellShippingOrderProcessor
	pffShippingOrderProcessor            *processor.PffShippingOrderProcessor
	shopeeShippingOrderProcessor         *processor.ShopeeShippingOrderProcessor
	cb3pfShippingOrderProcess            *processor.Cb3pfShippingOrderProcessor
	sellerShippingOrderProcessor         *processor.SellerShippingOrderProcessor
	weightDimensionProcessor             *processor.WeightDimensionProcessor
	groupShipmentOrderProcessor          *processor.GroupShipmentOrderProcessor

	// 分组规则处理器
	groupingRulesProcessor processor.GroupingRulesProcessor
}

// NewItemGrouper 创建 DefaultItemGrouper 的新实例。
// 此构造函数遵循依赖注入模式，以确保可测试性和模块化。
//
// 参数包括不同履行类型的所有必要处理器和一个规则处理器。
// 每个处理器处理特定业务逻辑，以应对不同的寻源场景。
func NewItemGrouper(
	confAccessor config.ConfAccessor,
	advanceBookingShippingOrderProcessor *processor.AdvanceBookingShippingOrderProcessor,
	cbLFFShippingOrderProcessor *processor.CBLFFShippingOrderProcessor,
	resellShippingOrderProcessor *processor.ResellShippingOrderProcessor,
	pffShippingOrderProcessor *processor.PffShippingOrderProcessor,
	shopeeShippingOrderProcessor *processor.ShopeeShippingOrderProcessor,
	cb3pfShippingOrderProcess *processor.Cb3pfShippingOrderProcessor,
	sellerShippingOrderProcessor *processor.SellerShippingOrderProcessor,
	weightDimensionProcessor *processor.WeightDimensionProcessor,
	groupShipmentOrderProcessor *processor.GroupShipmentOrderProcessor,
	groupingRulesProcessor processor.GroupingRulesProcessor,
) *DefaultItemGrouper {
	return &DefaultItemGrouper{
		configAccessor:                       confAccessor,
		advanceBookingShippingOrderProcessor: advanceBookingShippingOrderProcessor,
		cbLFFShippingOrderProcessor:          cbLFFShippingOrderProcessor,
		resellShippingOrderProcessor:         resellShippingOrderProcessor,
		pffShippingOrderProcessor:            pffShippingOrderProcessor,
		shopeeShippingOrderProcessor:         shopeeShippingOrderProcessor,
		cb3pfShippingOrderProcess:            cb3pfShippingOrderProcess,
		sellerShippingOrderProcessor:         sellerShippingOrderProcessor,
		weightDimensionProcessor:             weightDimensionProcessor,
		groupShipmentOrderProcessor:          groupShipmentOrderProcessor,
		groupingRulesProcessor:               groupingRulesProcessor,
	}
}

func (g *DefaultItemGrouper) GroupItems(
	ctx context.Context, shippingOrders []group_entity.ShippingOrder, buyerInfo group_entity.BuyerInfo,
	rules *group_entity.GroupingRules, skipSplitByWeightDimension bool,
) ([]group_entity.ShippingOrder, fsserr.Error) {

	// 参数验证
	if len(shippingOrders) == 0 {
		return []group_entity.ShippingOrder{}, nil
	}

	orders := shippingOrders
	singleSourceRules := g.extractSingleSourceRules(rules)

	// 前置步骤：合并同一个Shop的Items到同一个ShippingOrder
	orders = g.mergeItemsBySameShop(ctx, orders)

	// 第一步：应用 BundleRule（捆绑规则）
	orders, err := g.applyBundleRules(ctx, orders, rules)
	if err != nil {
		return nil, err
	}

	// 第二步：执行所有 Processor（除 weightDimensionProcessor）
	orders, err = g.executeProcessors(ctx, orders, buyerInfo, singleSourceRules)
	if err != nil {
		return nil, err
	}

	// 第三步：应用其他分组规则（隔离、渠道规则）
	orders, err = g.applyOtherRules(ctx, orders, rules)
	if err != nil {
		return nil, err
	}

	// 第四步：执行 weightDimensionProcessor
	orders, err = g.executeWeightDimensionProcessor(ctx, orders, buyerInfo, skipSplitByWeightDimension, singleSourceRules)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// mergeItemsBySameShop 合并同一个Shop的Items到同一个ShippingOrder
// 这是一个前置步骤，确保来自同一个店铺的商品默认被分组到同一个配送订单中
//
// 参数:
//   - ctx: 上下文
//   - orders: 原始的配送订单列表
//
// 返回: 按店铺合并后的配送订单列表
func (g *DefaultItemGrouper) mergeItemsBySameShop(ctx context.Context, orders []group_entity.ShippingOrder) []group_entity.ShippingOrder {
	if len(orders) == 0 {
		return orders
	}

	// 使用map按ShopID分组所有Items
	shopIDToItems := make(map[uint64][]group_entity.ShippingOrderItem)
	var firstOrderByShop = make(map[uint64]group_entity.ShippingOrder) // 保存每个店铺的第一个订单作为模板

	for _, order := range orders {
		for _, item := range order.Items {
			// 处理 Package Items：如果 Package 包含来自多个店铺的商品，需要拆分
			if item.IsPackageAndFromMultipleShop() {
				g.handleMultiShopPackageItem(item, order, shopIDToItems, firstOrderByShop)
			} else {
				// 普通情况：使用 GetShopInfo() 获取店铺ID
				shopID := item.GetShopInfo().ShopID
				shopIDToItems[shopID] = append(shopIDToItems[shopID], item)

				// 如果是该店铺的第一个订单，保存作为模板
				if _, exists := firstOrderByShop[shopID]; !exists {
					firstOrderByShop[shopID] = order
				}
			}
		}
	}

	// 为每个店铺创建新的ShippingOrder
	var mergedOrders []group_entity.ShippingOrder
	for shopID, items := range shopIDToItems {
		templateOrder := firstOrderByShop[shopID]

		// 创建新的订单，使用第一个订单作为模板，但替换为合并后的Items
		mergedOrder := group_entity.ShippingOrder{
			RequireAdvanceBooking:       templateOrder.RequireAdvanceBooking,
			Items:                       items,
			FulfilmentInfo:              templateOrder.FulfilmentInfo,
			CacheFallbackFulfilmentInfo: templateOrder.CacheFallbackFulfilmentInfo,
			IsDecoupled:                 templateOrder.IsDecoupled,
			IsGroupShipment:             templateOrder.IsGroupShipment,
			GroupShipmentInfo:           templateOrder.GroupShipmentInfo,
			IsChoiceEligible:            templateOrder.IsChoiceEligible,
			IsTradeInEligible:           templateOrder.IsTradeInEligible,
			EnabledChoiceChannels:       templateOrder.EnabledChoiceChannels,
			EnabledTradeInChannels:      templateOrder.EnabledTradeInChannels,
		}

		mergedOrders = append(mergedOrders, mergedOrder)
	}

	logger.CtxLogInfof(ctx, "merged items by shop: original %d orders -> %d orders", len(orders), len(mergedOrders))
	return mergedOrders
}

// handleMultiShopPackageItem 处理包含多个店铺商品的Package Item
// 将 Package Item 按店铺拆分成多个独立的普通商品
func (g *DefaultItemGrouper) handleMultiShopPackageItem(
	packageItem group_entity.ShippingOrderItem,
	templateOrder group_entity.ShippingOrder,
	shopIDToItems map[uint64][]group_entity.ShippingOrderItem,
	firstOrderByShop map[uint64]group_entity.ShippingOrder,
) {
	// 按店铺分组 PackageItems 中的商品
	shopIDToPackageItems := make(map[uint64][]group_entity.ShippingOrderItem)
	for _, subItem := range packageItem.PackageItems {
		shopID := subItem.GetShopInfo().ShopID
		shopIDToPackageItems[shopID] = append(shopIDToPackageItems[shopID], subItem)
	}

	// 为每个店铺创建新的 ShippingOrderItem
	for shopID, subItems := range shopIDToPackageItems {
		// 基于原 PackageItem 创建新的 Item，但只包含该店铺的 PackageItems
		newItem := group_entity.ShippingOrderItem{
			QueryId:                   packageItem.QueryId,
			ShopInfo:                  subItems[0].ShopInfo, // 使用第一个子商品的ShopInfo
			ItemID:                    packageItem.ItemID,
			ModelID:                   packageItem.ModelID,
			Quantity:                  packageItem.Quantity,
			ItemType:                  packageItem.ItemType,
			FulfilmentTypeToLocations: packageItem.FulfilmentTypeToLocations,
			IsSBSItem:                 packageItem.IsSBSItem,
			BundleDealID:              packageItem.BundleDealID,
			ItemGroupID:               packageItem.ItemGroupID,
			AddOnDealID:               packageItem.AddOnDealID,
			SingleSource:              packageItem.SingleSource,
			IsBOMItem:                 packageItem.IsBOMItem,
			PackageItems:              subItems, // 只包含该店铺的商品
		}

		shopIDToItems[shopID] = append(shopIDToItems[shopID], newItem)

		// 如果是该店铺的第一个订单，保存作为模板
		if _, exists := firstOrderByShop[shopID]; !exists {
			firstOrderByShop[shopID] = templateOrder
		}
	}
}

// extractSingleSourceRules 提取 Single Source 约束规则
func (g *DefaultItemGrouper) extractSingleSourceRules(rules *group_entity.GroupingRules) []group_entity.ConstraintRule {
	var singleSourceRules []group_entity.ConstraintRule
	if rules != nil {
		for _, rule := range rules.ConstraintRules {
			if rule.ConstraintType == group_entity.ConstraintTypeSingleSourceOnly {
				singleSourceRules = append(singleSourceRules, rule)
			}
		}
	}
	return singleSourceRules
}

// applyBundleRules 应用捆绑规则
func (g *DefaultItemGrouper) applyBundleRules(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	rules *group_entity.GroupingRules,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	if rules != nil && len(rules.BundleRules) > 0 {
		bundledOrders, err := g.groupingRulesProcessor.ApplyBundleRules(ctx, orders, rules.BundleRules)
		if err != nil {
			return nil, err
		}
		return bundledOrders, nil
	}
	return orders, nil
}

// executeProcessors 执行所有 Processor（除 weightDimensionProcessor）
func (g *DefaultItemGrouper) executeProcessors(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	buyerInfo group_entity.BuyerInfo,
	singleSourceRules []group_entity.ConstraintRule,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	processors := []processor.ShippingOrderProcessor{
		g.advanceBookingShippingOrderProcessor,
		g.cbLFFShippingOrderProcessor,
		g.resellShippingOrderProcessor,
		g.pffShippingOrderProcessor,
		g.shopeeShippingOrderProcessor,
		g.cb3pfShippingOrderProcess,
		g.sellerShippingOrderProcessor,
		g.groupShipmentOrderProcessor,
	}

	for _, proc := range processors {
		processedOrders, err := g.executeProcessor(ctx, orders, buyerInfo, proc)
		if err != nil {
			return nil, err
		}
		orders = processedOrders

		// 在每个 Processor 执行后立即检查 Single Source 约束
		if len(singleSourceRules) > 0 {
			if err := g.validateSingleSourceConstraints(ctx, orders, singleSourceRules, string(proc.Name())); err != nil {
				return nil, err
			}
		}
	}
	return orders, nil
}

// executeProcessor 执行单个 Processor
func (g *DefaultItemGrouper) executeProcessor(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	buyerInfo group_entity.BuyerInfo,
	proc processor.ShippingOrderProcessor,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	var processingOrders, skipOrders []group_entity.ShippingOrder

	// 分离可处理和跳过的订单
	for _, order := range orders {
		if proc.IsAbleToProcess(ctx, buyerInfo, order) {
			processingOrders = append(processingOrders, order)
		} else {
			skipOrders = append(skipOrders, order)
		}
	}

	// 如果没有需要处理的订单，跳过当前处理器
	if len(processingOrders) == 0 {
		return orders, nil
	}

	// 处理订单
	processedOrders, err := proc.DoProcess(ctx, buyerInfo, processingOrders)
	if err != nil {
		return nil, fsserr.New(fsserr.ServerErr, "processor %s failed: %v", proc.Name(), err)
	}

	// 合并处理后的订单和跳过的订单
	return append(skipOrders, processedOrders...), nil
}

// applyOtherRules 应用除 BundleRule 和 SingleSource 之外的其他分组规则
func (g *DefaultItemGrouper) applyOtherRules(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	rules *group_entity.GroupingRules,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	if rules != nil && !rules.IsEmpty() {
		filteredRules := g.createFilteredRules(rules)
		if !filteredRules.IsEmpty() {
			finalOrders, err := g.groupingRulesProcessor.ApplyNonBundleRules(ctx, orders, filteredRules)
			if err != nil {
				return nil, err
			}
			return finalOrders, nil
		}
	}
	return orders, nil
}

// executeWeightDimensionProcessor 执行重量尺寸处理器
func (g *DefaultItemGrouper) executeWeightDimensionProcessor(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	buyerInfo group_entity.BuyerInfo,
	skipSplitByWeightDimension bool,
	singleSourceRules []group_entity.ConstraintRule,
) ([]group_entity.ShippingOrder, fsserr.Error) {
	shouldEnable := g.shouldEnableWeightDimensionProcessor(ctx, buyerInfo, skipSplitByWeightDimension)
	if !shouldEnable {
		return orders, nil
	}

	var processingOrders, skipOrders []group_entity.ShippingOrder

	// 分离可处理和跳过的订单
	for _, order := range orders {
		if g.weightDimensionProcessor.IsAbleToProcess(ctx, buyerInfo, order) {
			processingOrders = append(processingOrders, order)
		} else {
			skipOrders = append(skipOrders, order)
		}
	}

	// 如果有需要处理的订单，进行处理
	if len(processingOrders) > 0 {
		processedOrders, err := g.weightDimensionProcessor.DoProcess(ctx, buyerInfo, processingOrders)
		if err != nil {
			return nil, fsserr.New(fsserr.ServerErr, "weightDimensionProcessor failed: %v", err)
		}
		orders = append(skipOrders, processedOrders...)

		// 重量尺寸处理器可能会拆分订单，需要再次检查 Single Source 约束
		if len(singleSourceRules) > 0 {
			if err := g.validateSingleSourceConstraints(ctx, orders, singleSourceRules, string(g.weightDimensionProcessor.Name())); err != nil {
				return nil, err
			}
		}
	}

	return orders, nil
}

// shouldEnableWeightDimensionProcessor 判断是否应该启用重量尺寸处理器
// 参考 fulfilment_planning 的逻辑：
// 1. 首先检查 SkipSplitByWeightDimension 标志，如果为 true 则直接跳过
// 2. 检查用户是否在白名单中
// 3. 检查用户ID的哈希值是否在灰度百分比范围内
// 参数:
//   - ctx: 上下文
//   - buyerInfo: 买家信息
//   - skipSplitByWeightDimension: 是否跳过超材拆单标志
//
// 返回: 是否应该启用重量尺寸处理器
func (g *DefaultItemGrouper) shouldEnableWeightDimensionProcessor(ctx context.Context, buyerInfo group_entity.BuyerInfo, skipSplitByWeightDimension bool) bool {
	// 参考 fulfilment_planning 的逻辑：如果 SkipSplitByWeightDimension 为 true，则跳过重量尺寸处理器
	if skipSplitByWeightDimension {
		return false
	}

	// 获取重量尺寸处理器的配置
	percentage := g.configAccessor.GetWeightDimOrderSplitPercentage(ctx)
	whitelist := g.configAccessor.GetWeightDimOrderSplitWhitelist(ctx)

	// 检查用户是否在白名单中
	for _, userID := range whitelist {
		if userID == uint64(buyerInfo.UserID) {
			return true
		}
	}

	// 基于用户ID的哈希值进行灰度发布
	// 使用与 fulfilment_planning 相同的逻辑：percentage > int(buyerInfo.UserID)%100
	if percentage > int(buyerInfo.UserID)%100 {
		return true
	}

	return false
}

// validateSingleSourceConstraints 在寻源过程中验证 Single Source 约束
// 这个方法在每个 Processor 执行后立即调用，确保寻源过程中不违反约束
//
// 参数:
//   - ctx: 上下文
//   - orders: 当前的订单列表
//   - singleSourceRules: Single Source 约束规则列表
//   - processorName: 当前执行的处理器名称（用于错误信息）
//
// 返回: 如果违反约束则返回错误
func (g *DefaultItemGrouper) validateSingleSourceConstraints(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	singleSourceRules []group_entity.ConstraintRule,
	processorName string,
) fsserr.Error {
	for _, rule := range singleSourceRules {
		if err := g.validateSingleSourceRule(ctx, orders, rule, processorName); err != nil {
			if rule.Mandatory {
				return err
			}
			// 非强制性规则，记录警告后继续
			logger.CtxLogInfof(ctx, "single source rule %s not met after %s: %v", rule.RuleID, processorName, err)
		}
	}
	return nil
}

// validateSingleSourceRule 验证单个 Single Source 规则
func (g *DefaultItemGrouper) validateSingleSourceRule(
	ctx context.Context,
	orders []group_entity.ShippingOrder,
	rule group_entity.ConstraintRule,
	processorName string,
) fsserr.Error {
	queryIDSet := collection.NewSetFromSlice(rule.ItemQueryIDs)
	queryIDToSource := make(map[string]string)

	for _, order := range orders {
		if err := g.validateOrderForSingleSource(order, queryIDSet, queryIDToSource, processorName); err != nil {
			return err
		}
	}
	return nil
}

// validateOrderForSingleSource 验证单个订单的 Single Source 约束
func (g *DefaultItemGrouper) validateOrderForSingleSource(
	order group_entity.ShippingOrder,
	queryIDSet collection.Set[string],
	queryIDToSource map[string]string,
	processorName string,
) fsserr.Error {
	// 只检查已经选定发货源的订单
	if !order.IsSourceSelected() {
		return nil
	}

	source := order.FulfilmentInfo.Source
	for _, item := range order.Items {
		if err := g.validateItemForSingleSource(item, source, queryIDSet, queryIDToSource, processorName); err != nil {
			return err
		}
	}
	return nil
}

// validateItemForSingleSource 验证单个商品的 Single Source 约束
func (g *DefaultItemGrouper) validateItemForSingleSource(
	item group_entity.ShippingOrderItem,
	source string,
	queryIDSet collection.Set[string],
	queryIDToSource map[string]string,
	processorName string,
) fsserr.Error {
	if !queryIDSet.Contains(item.QueryId) {
		return nil
	}

	existingSource, exists := queryIDToSource[item.QueryId]
	if !exists {
		queryIDToSource[item.QueryId] = source
		return nil
	}

	if existingSource != source {
		return fsserr.New(fsserr.ParamErr,
			"item %s violates single source constraint after %s: found in sources %s and %s",
			item.QueryId, processorName, existingSource, source)
	}

	return nil
}

// createFilteredRules 创建过滤后的规则副本，排除 Single Source 约束
// 因为 Single Source 约束已经在寻源过程中检查过了，所以在最后的规则应用阶段需要排除
func (g *DefaultItemGrouper) createFilteredRules(rules *group_entity.GroupingRules) *group_entity.GroupingRules {
	if rules == nil {
		return nil
	}

	filteredRules := &group_entity.GroupingRules{
		BundleRules:     []group_entity.BundleRule{},     // Bundle 规则已经在第一步处理过了
		IsolationRules:  rules.IsolationRules,            // 保留隔离规则
		ChannelRules:    rules.ChannelRules,              // 保留渠道规则
		ConstraintRules: []group_entity.ConstraintRule{}, // 创建过滤后的约束规则
	}

	// 只保留非 Single Source 的约束规则
	for _, rule := range rules.ConstraintRules {
		if rule.ConstraintType != group_entity.ConstraintTypeSingleSourceOnly {
			filteredRules.ConstraintRules = append(filteredRules.ConstraintRules, rule)
		}
	}

	return filteredRules
}
