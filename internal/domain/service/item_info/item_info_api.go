package item_info

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ItemInfoApi interface {
	GetProductInfo(ctx context.Context, shopItemIDs []entity.ShopItemID, needDeleted bool) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error)
}

type ItemInfoApiImpl struct {
	configAccessor config.ConfAccessor
	itemInfoCache  lru.Cache[*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo]
	spexClient     spexlib.SpexClient
}

func NewItemInfoApiImpl(configAccessor config.ConfAccessor, spexClient spexlib.SpexClient) (*ItemInfoApiImpl, error) {
	itemInfoCache, err := lru.NewLruCache[*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo](cache.ItemInfoLruName)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	return &ItemInfoApiImpl{
		configAccessor: configAccessor,
		itemInfoCache:  itemInfoCache,
		spexClient:     spexClient,
	}, nil
}

func (i *ItemInfoApiImpl) GetProductInfo(ctx context.Context, shopItemIDs []entity.ShopItemID, needDeleted bool) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error) {
	shopItemIDToItemInfo, err := cache.MultiLoadManyFromSlice(
		ctx,
		i.itemInfoCache,
		shopItemIDs,
		i.shopItemIDToCacheKey,
		func(ctx context.Context, inputIDs []entity.ShopItemID) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error) {
			return i.getProductInfo(ctx, inputIDs, needDeleted)
		},
		func(productInfo *marketplace_listing_item_itemaggregation_iteminfo.ProductInfo) entity.ShopItemID {
			return entity.ShopItemID{
				ShopID: uint64(productInfo.GetShopId()),
				ItemID: typ.ItemIdType(productInfo.GetItemId()),
			}
		},
	)
	if err != nil {
		return nil, err
	}

	return collection.MapToSlice(shopItemIDToItemInfo), nil
}

func (i *ItemInfoApiImpl) getProductInfo(ctx context.Context, shopItemIDs []entity.ShopItemID, needDeleted bool) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error) {
	caller := concurrency.NewConcurrencySplitCaller[entity.ShopItemID, *marketplace_listing_item_itemaggregation_iteminfo.ProductInfo]()
	return caller.Call(
		ctx,
		shopItemIDs,
		i.configAccessor.GetProductInfoAPISize(ctx),
		func(ctx context.Context, queries []entity.ShopItemID) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error) {
			return i.getProductInfoWithSpex(ctx, queries, needDeleted)
		},
	)
}

func (i *ItemInfoApiImpl) getProductInfoWithSpex(ctx context.Context, shopItemIDs []entity.ShopItemID, needDeleted bool) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error) {
	queries := make([]*marketplace_listing_item_itemaggregation_iteminfo.ShopItemId, len(shopItemIDs))
	for i, shopItemID := range shopItemIDs {
		queries[i] = &marketplace_listing_item_itemaggregation_iteminfo.ShopItemId{
			ShopId: proto.Uint32(uint32(shopItemID.ShopID)),
			ItemId: proto.Uint64(uint64(shopItemID.ItemID)),
		}
	}

	req := &marketplace_listing_item_itemaggregation_iteminfo.GetProductInfoRequest{
		ShopItemIds: queries,
		InfoTypes: []uint32{
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_ITEM_BASIC),
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_MODEL_BASIC),
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_PRICE),
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_CATEGORY),
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_LOGISTICS),
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_ATTRIBUTE),
			uint32(marketplace_listing_item_itemaggregation_iteminfo.Constant_ITEM_INSTALLATION_INFO),
		},
		NeedDeleted: proto.Bool(needDeleted),
	}

	productInfos, err := i.spexClient.GetProductInfo(ctx, req)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}
	return productInfos, nil
}

func (i *ItemInfoApiImpl) shopItemIDToCacheKey(shopItemID entity.ShopItemID) string {
	b := strings.Builder{}
	b.WriteString("item.info.v7.")
	b.WriteString(strconv.FormatUint(shopItemID.ShopID, 10))
	b.WriteString(".")
	b.WriteString(strconv.FormatUint(uint64(shopItemID.ItemID), 10))
	return b.String()
}
