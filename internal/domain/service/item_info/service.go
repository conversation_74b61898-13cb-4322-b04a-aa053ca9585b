package item_info

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

type ItemService interface {
	//GetShopItemIDToItemInfo(ctx context.Context, itemGroups []entity.ItemGroup) map[entity.ShopItemID]entity.ItemInfo
	//ItemInfo(ctx context.Context, shopItemID entity.ShopItemID) (entity.ItemInfo, error)
	BatchItemInfo(ctx context.Context, shopItemIDs []entity.ShopItemID) (map[entity.ShopItemID]entity.ItemInfo, error)
	//PrefetchItemInfos(ctx context.Context, shopItemIDs []entity.ShopItemID)
	EnabledChannelIDsWithPFFValidation(ctx context.Context, itemInfo entity.ItemInfo, manageBySBS bool, warehouseCodes []string) []int
}

type ItemInfoServiceImpl struct {
	api            ItemInfoApi
	configAccessor config.ConfAccessor
}

func NewItemInfoServiceImpl(api ItemInfoApi, configAccessor config.ConfAccessor) *ItemInfoServiceImpl {
	return &ItemInfoServiceImpl{
		api:            api,
		configAccessor: configAccessor,
	}
}

func (i *ItemInfoServiceImpl) BatchItemInfo(ctx context.Context, shopItemIDs []entity.ShopItemID) (map[entity.ShopItemID]entity.ItemInfo, error) {
	result, err := i.api.GetProductInfo(ctx, shopItemIDs, true)
	if err != nil {
		return nil, err
	}

	groupSellerCoverShippingFeeConfig := i.configAccessor.GetGroupSellerCoverShippingFeeConfig(ctx)
	idToItemInfo := make(map[entity.ShopItemID]entity.ItemInfo)
	for _, val := range result {
		itemInfo, err := BuildItemInfoFromProductInfo(ctx, val, groupSellerCoverShippingFeeConfig)
		if err != nil {
			return nil, err
		}
		idToItemInfo[entity.ShopItemID{
			ShopID: uint64(itemInfo.ShopID),
			ItemID: itemInfo.ItemID,
		}] = itemInfo
	}

	return idToItemInfo, nil
}

func (i *ItemInfoServiceImpl) EnabledChannelIDsWithPFFValidation(ctx context.Context, itemInfo entity.ItemInfo, manageBySBS bool, warehouseCodes []string) []int {
	var result []int

	for channelID, logisticsInfo := range itemInfo.ChannelIDToLogistics {
		isOldStructure := logisticsInfo.IsSellerChannel == nil && logisticsInfo.WarehouseLocationList == nil
		if isOldStructure {
			if logisticsInfo.Enabled {
				result = append(result, channelID)
			}
			continue
		}

		if len(warehouseCodes) == 0 {
			continue
		}

		enabledForAllWarehouse := true
		for _, warehouseCode := range warehouseCodes {
			var enabledWarehouse bool
			if warehouseCode != "" {
				if manageBySBS && logisticsInfo.WarehouseLocationList != nil {
					for _, id := range logisticsInfo.WarehouseLocationList {
						if warehouseCode == id {
							enabledWarehouse = true
							break
						}
					}
				}
				if !manageBySBS && logisticsInfo.IsSellerChannel != nil {
					enabledWarehouse = *logisticsInfo.IsSellerChannel
				}
			} else {
				if logisticsInfo.IsSellerChannel != nil {
					enabledWarehouse = *logisticsInfo.IsSellerChannel
				} else {
					enabledWarehouse = logisticsInfo.Enabled
				}
			}
			if !enabledWarehouse {
				enabledForAllWarehouse = false
				break
			}
		}
		if enabledForAllWarehouse {
			result = append(result, channelID)
		}
	}

	channelFilterForPreorder := i.configAccessor.GetFilterChannelsForPreorder(ctx)
	if len(channelFilterForPreorder) > 0 && itemInfo.IsPreOrder {
		channelFilterSet := collection.NewSetFromSlice(channelFilterForPreorder)
		filteredChannels := make([]int, 0)
		for _, channel := range result {
			if channelFilterSet.Contains(channel) {
				continue
			}
			filteredChannels = append(filteredChannels, channel)
		}

		return filteredChannels
	}

	return result
}
