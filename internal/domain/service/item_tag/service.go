package item_tag

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

type ItemTagService interface {
	BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []typ.ItemIdType) (map[uint64][]uint64, error)
	BatchCheckItemContainsTag(ctx context.Context, itemIDs []typ.ItemIdType, tagID uint64) (map[uint64]bool, error)
}

type ItemTagServiceImpl struct {
	api TagApi
}

func NewItemTagServiceImpl(api TagApi) *ItemTagServiceImpl {
	return &ItemTagServiceImpl{api: api}
}

func (i *ItemTagServiceImpl) BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []typ.ModelIdType) (map[typ.ModelIdType][]uint64, error) {
	modelIDToModelLabel, err := i.api.BatchGetItemModelIDToLabels(ctx, itemModelIDs)
	if err != nil {
		return nil, err
	}

	modelIDToLabelIDs := make(map[typ.ModelIdType][]uint64)
	for _, modelLabel := range modelIDToModelLabel {
		modelIDToLabelIDs[typ.ModelIdType(modelLabel.GetModelId())] = modelLabel.GetLabelIdList()
	}
	return modelIDToLabelIDs, nil
}

func (i *ItemTagServiceImpl) BatchCheckItemContainsTag(ctx context.Context, itemIDs []typ.ItemIdType, tagID uint64) (map[typ.ItemIdType]bool, error) {
	itemTags, err := i.batchItemTags(ctx, itemIDs, []uint64{tagID})
	if err != nil {
		return nil, err
	}

	hasTag := make(map[typ.ItemIdType]bool, len(itemTags))
	for _, itemTag := range itemTags {
		hasTag[itemTag.ItemID] = itemTag.IsTagged
	}

	return hasTag, nil
}

func (i *ItemTagServiceImpl) batchItemTags(ctx context.Context, itemIDs []typ.ItemIdType, tagIDs []uint64) ([]entity.ItemTag, error) {
	itemTags := make([]entity.ItemTag, len(itemIDs)*len(tagIDs))
	ids := make([]entity.ItemTagID, 0)
	for _, itemID := range itemIDs {
		for _, tagID := range tagIDs {
			itemTagID := entity.ItemTagID{ItemID: itemID, TagID: tagID}
			ids = append(ids, itemTagID)
		}
	}
	idToTag, err := i.api.GetItemLabels(ctx, ids)
	if err != nil {
		return nil, err
	}

	for i, id := range ids {
		itemTags[i] = idToTag[id]
	}

	return itemTags, nil
}
