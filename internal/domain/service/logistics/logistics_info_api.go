package logistics

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/lpslib"
)

type LogisticsInfoApi interface {
	GetAllChannelsWeightDim(
		ctx context.Context,
		orders []lpslib.OrderSplitLogisticsQuery,
	) ([]lpslib.ChannelWeightDimensionInfo, error)
}

type LogisticsInfoApiImpl struct {
	lpsClient lpslib.LpsClient
}

func NewLogisticsInfoApiImpl(lpsClient lpslib.LpsClient) *LogisticsInfoApiImpl {
	return &LogisticsInfoApiImpl{
		lpsClient: lpsClient,
	}
}

func (i *LogisticsInfoApiImpl) GetAllChannelsWeightDim(
	ctx context.Context,
	orders []lpslib.OrderSplitLogisticsQuery,
) ([]lpslib.ChannelWeightDimensionInfo, error) {
	caller := concurrency.NewConcurrencySplitCaller[lpslib.OrderSplitLogisticsQuery, lpslib.ChannelWeightDimensionInfo]()
	resp, err := caller.Call(ctx, orders, 20, func(ctx context.Context, order []lpslib.OrderSplitLogisticsQuery) ([]lpslib.ChannelWeightDimensionInfo, error) {
		resp, err := i.lpsClient.ShoppingCartValidation(ctx, &lpslib.GetAllChannelsWeightDimReq{Orders: orders})
		if err != nil {
			return nil, err
		}
		return resp.Data, nil
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}
