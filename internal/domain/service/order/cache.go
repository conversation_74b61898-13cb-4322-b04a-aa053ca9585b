package order

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"git.garena.com/shopee/bg-logistics/go/go-redis"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
)

type CacheStore interface {
	AcquireDistributedLock(ctx context.Context, shopID uint64, isFulfilledByShopee bool, timestamp int64) (string, string, error)
	ReleaseDistributedLock(ctx context.Context, lock, lockValue string) bool
	IncreaseSalesOrderCount(ctx context.Context, shopID uint64, isFulfilledByShopee bool, timestamp int64, delta uint64) (int64, error)
	GetSalesOrderCount(ctx context.Context, shopID uint64, isFulfilledByShopee bool, timestamp int64) (int64, error)
}

type CacheStoreImpl struct {
	lockClient  *redis.Client
	countClient *redis.Client
}

func NewCacheStore(clients redishelper.GlobalRedisClients) *CacheStoreImpl {
	lockClient, _ := clients.GetRedisClusterByClusterName(redishelper.Default)
	countClient, _ := clients.GetRedisClusterByClusterName(redishelper.Static)
	return &CacheStoreImpl{
		lockClient:  lockClient,
		countClient: countClient,
	}
}

func (r *CacheStoreImpl) AcquireDistributedLock(ctx context.Context, shopID uint64, isFulfilledByShopee bool, timestamp int64) (string, string, error) {
	lockValue := uuid.New().String()
	deadline := time.Now().Add(3 * time.Second)
	key := r.getDistributedLockKey(shopID, isFulfilledByShopee, timestamp)
	for time.Now().Before(deadline) {
		ok, err := r.lockClient.
			SetNX(ctx, key, lockValue, 3*time.Second).
			Result()
		if err != nil {
			return "", "", err
		}
		if ok {
			return key, lockValue, nil
		}
		time.Sleep(100 * time.Millisecond)
	}
	return "", "", fmt.Errorf("failed to acquire lock")
}

func (r *CacheStoreImpl) ReleaseDistributedLock(ctx context.Context, lock, lockValue string) bool {
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end`
	res, err := r.lockClient.Eval(ctx, luaScript, []string{lock}, lockValue).Result()
	return err == nil && res.(int64) == 1
}

func (r *CacheStoreImpl) IncreaseSalesOrderCount(ctx context.Context, shopID uint64, isFulfilledByShopee bool, timestamp int64, delta uint64) (int64, error) {
	count, err := r.GetSalesOrderCount(ctx, shopID, isFulfilledByShopee, timestamp)
	if err != nil && !redishelper.IsNil(err) {
		return 0, err
	}
	count += int64(delta)
	err = r.countClient.Set(ctx, r.getSalesOrderCountCacheKey(shopID, isFulfilledByShopee, timestamp), count, 7*24*time.Hour).Err()
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (r *CacheStoreImpl) GetSalesOrderCount(ctx context.Context, shopID uint64, isFulfilledByShopee bool, timestamp int64) (int64, error) {
	count, err := r.countClient.Get(ctx, r.getSalesOrderCountCacheKey(shopID, isFulfilledByShopee, timestamp)).Int64()
	return count, err
}

func (r *CacheStoreImpl) getSalesOrderCountCacheKey(shopID uint64, isFulfilledByShopee bool, timestamp int64) string {
	t := time.Unix(timestamp, 0).In(time.Local)
	midnight := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local).Unix()
	fulfillType := "shopee"
	if !isFulfilledByShopee {
		fulfillType = "seller"
	}
	return fmt.Sprintf("shop_sales_order_count:%d:%s:%d", shopID, fulfillType, midnight)
}

func (r *CacheStoreImpl) getDistributedLockKey(shopID uint64, isFulfilledByShopee bool, timestamp int64) string {
	return fmt.Sprintf("lock.%s", r.getSalesOrderCountCacheKey(shopID, isFulfilledByShopee, timestamp))
}
