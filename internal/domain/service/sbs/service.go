package sbs

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_fulfilment_sbs.pb"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
)

type Service interface {
	GetSBSShopItemInfos(ctx context.Context, shopIDToItemModelIDs map[uint64][]entity.ItemModelID) ([]entity.SBSShopInfo, error)
	BatchGetSBSShipmentGroups(ctx context.Context, groupIDs []uint32, lang string) (map[uint32]entity.SBSShipmentGroup, error)
}

type ServiceImpl struct {
	layerCache layer_cache.LayerCache
	spexClient spexlib.SpexClient
}

func NewServiceImpl(ctx context.Context, layerCacheManager *layer_cache.LayerCacheManager, spexClient spexlib.SpexClient) (*ServiceImpl, error) {
	layerCache, err := layerCacheManager.GetCacheByNamespace(ctx, layer_cache.GetSBSShipmentGroups)
	if err != nil {
		return nil, err
	}
	return &ServiceImpl{
		layerCache: layerCache,
		spexClient: spexClient,
	}, nil
}

func (s *ServiceImpl) GetSBSShopItemInfos(
	ctx context.Context,
	shopIDToItemModelIDs map[uint64][]entity.ItemModelID,
) ([]entity.SBSShopInfo, error) {
	sbsShopItemInfos, err := s.getSBSItemInfo(ctx, shopIDToItemModelIDs)
	if err != nil {
		return nil, err
	}

	var shopInfos []entity.SBSShopInfo
	for _, info := range sbsShopItemInfos {
		var itemModelInfos []entity.SBSItemModelInfo
		for _, itemInfo := range info.GetItems() {
			itemModelInfos = append(itemModelInfos, entity.SBSItemModelInfo{
				ItemID:        typ.ItemIdType(itemInfo.GetItemId()),
				ModelID:       typ.ModelIdType(itemInfo.GetModelId()),
				IsSBSItem:     itemInfo.GetIsSbsItem(),
				GroupShipment: itemInfo.GetGroupShipment(),
			})
		}

		shopInfo := entity.SBSShopInfo{
			ShopID:          info.GetShopId(),
			IsSBSShop:       info.GetIsSbsShop(),
			WarehouseID:     info.GetWarehouseId(),
			Service:         info.GetService(),
			ShipmentGroupID: uint32(info.GetShipmentGroupId()),
			ItemModelInfos:  itemModelInfos,
		}

		shopInfos = append(shopInfos, shopInfo)
	}

	return shopInfos, nil
}

func (s *ServiceImpl) getSBSItemInfo(ctx context.Context, shopIDToItemModelIDs map[uint64][]entity.ItemModelID) ([]*marketplace_order_processing_fulfilment_sbs.SbsShopItemInfo, error) {
	var shopItems []*marketplace_order_processing_fulfilment_sbs.ItemParam
	for shopID, itemModelIDs := range shopIDToItemModelIDs {
		var ItemModelParams []*marketplace_order_processing_fulfilment_sbs.ItemModelParam
		for _, itemModelID := range itemModelIDs {
			ItemModelParams = append(ItemModelParams, &marketplace_order_processing_fulfilment_sbs.ItemModelParam{
				ItemId:  proto.Uint64(uint64(itemModelID.ItemID)),
				ModelId: proto.Uint64(uint64(itemModelID.ModelID)),
			})
		}
		shopItems = append(shopItems, &marketplace_order_processing_fulfilment_sbs.ItemParam{
			ShopId:        proto.Uint64(shopID),
			ItemModelList: ItemModelParams,
		})
	}

	response, err := s.spexClient.GetSbsItemInfo(ctx, shopItems)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *ServiceImpl) BatchGetSBSShipmentGroups(ctx context.Context, groupIDs []uint32, lang string) (map[uint32]entity.SBSShipmentGroup, error) {
	sbsShipmentGroups, err := s.getSBSShipmentGroupsUsingCache(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	groupIDToShipmentGroup := make(map[uint32]entity.SBSShipmentGroup)
	for _, group := range sbsShipmentGroups {

		localisedDescription := parseLocalisedDescription(group, lang)
		shipmentGroup := entity.SBSShipmentGroup{
			ID:          group.GetGroupId(),
			Icon:        group.GetIcon(),
			Description: localisedDescription,
			Priority:    group.GetPriority(),
			NotGrouping: group.GetNotGrouping(),
		}
		groupIDToShipmentGroup[shipmentGroup.ID] = shipmentGroup
	}

	return groupIDToShipmentGroup, nil
}

func (s *ServiceImpl) getSBSShipmentGroupsUsingCache(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, error) {
	result, err := layer_cache.CacheLoadManyFromSlice[uint32, *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails](
		ctx,
		s.layerCache,
		groupIDs,
		getSBSShipmentGroupsKey,
		layer_cache.PbUnmarshalFromBytes,
		s.GetSBSShipmentGroups,
		layer_cache.WithCustomExpire(30*60),
	)
	if err != nil {
		return nil, err
	}

	return collection.MapValues(result), nil
}

func (s *ServiceImpl) GetSBSShipmentGroups(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, error) {
	caller := concurrency.NewConcurrencySplitCaller[uint32, *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails]()
	resp, err := caller.Call(ctx, groupIDs, 20, s.getSBSShipmentGroups)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *ServiceImpl) getSBSShipmentGroups(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, error) {
	response, err := s.spexClient.GetSBSShipmentGroups(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func parseLocalisedDescription(groupInfoDetails *marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, lang string) string {
	// parse Localised Description will fall back to original name if no translations are found
	fallback := groupInfoDetails.GetDescription()
	primaryContent := fallback
	for _, lc := range groupInfoDetails.GetLocalisedDescription() {
		if lc == nil {
			continue
		}

		if lc.GetLang() == lang && strings.TrimSpace(lc.GetLocalisedContent()) != "" {
			return lc.GetLocalisedContent() // early exit
		}

		if lc.GetIsPrimary() && strings.TrimSpace(lc.GetLocalisedContent()) != "" {
			primaryContent = lc.GetLocalisedContent() // replace primary content with valid translation
		}
	}
	return primaryContent
}

func getSBSShipmentGroupsKey(ctx context.Context, groupID uint32) string {
	return "sbs.group_shipment.v1" + strconv.FormatUint(uint64(groupID), 10)
}
