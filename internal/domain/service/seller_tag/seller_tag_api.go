package seller_tag

import (
	"context"
	"fmt"
	"strings"

	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_tag_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type SellerTagApi interface {
	GetShopSellerTagV2(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error)
	BatchGetShopSellerTags(ctx context.Context, shopIDs []uint64, tagNames []string) ([]*seller_seller_tag_core.EntityTag, error)
	BatchGetShopSellerTagsV2(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error)

	GetShopSellerTagV2WithCache(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error)
	BatchGetShopSellerTagsV2WithCache(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error)
}

type SellerTagApiImpl struct {
	SpexClient        spexlib.SpexClient
	configGetter      config.ConfAccessor
	entityTagLruCache lru.Cache[*seller_seller_tag_core.EntityTag]
	tagValueLruCache  lru.Cache[*seller_seller_tag_core.TagValue]
}

func NewSellerTagApiImpl(spexClient spexlib.SpexClient, configGetter config.ConfAccessor) (*SellerTagApiImpl, error) {
	entityTagLruCache, err := lru.NewLruCache[*seller_seller_tag_core.EntityTag](cache.EntityTagLruName)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	tagValueLruCache, err := lru.NewLruCache[*seller_seller_tag_core.TagValue](cache.TagValueLruName)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}

	return &SellerTagApiImpl{
		SpexClient:        spexClient,
		configGetter:      configGetter,
		entityTagLruCache: entityTagLruCache,
		tagValueLruCache:  tagValueLruCache,
	}, nil
}

func (s *SellerTagApiImpl) GetShopSellerTagV2(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error) {
	entities := make([]*seller_seller_tag_core.EntityTagIDs, 0)
	for _, sID := range shopIDs {
		e := &seller_seller_tag_core.EntityTagIDs{
			Entity: &seller_seller_tag_core.Entity{
				Id:   proto.Uint64(sID),
				Type: proto.Uint32(1),
			},
			TagNames: []string{tagName},
		}
		entities = append(entities, e)
	}
	size := s.configGetter.GetBatchGetEntityTagAPISize(ctx)
	caller := concurrency.NewConcurrencySplitCaller[*seller_seller_tag_core.EntityTagIDs, *seller_seller_tag_core.EntityTag]()
	entityTags, err := caller.Call(ctx, entities, size, func(ctx context.Context, queries []*seller_seller_tag_core.EntityTagIDs) ([]*seller_seller_tag_core.EntityTag, error) {
		req := &seller_seller_tag_core.BatchGetEntityTagRequest{
			EntityTagsList: queries,
		}
		entityTags, err := s.SpexClient.BatchGetEntityTag(ctx, req)
		if err != nil {
			return nil, err
		}
		return entityTags, nil
	})
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	return &seller_seller_tag_core.BatchGetEntityTagResponse{
		EntityTags: entityTags,
		DebugMsg:   nil,
	}, nil
}

func (s *SellerTagApiImpl) GetShopSellerTagV2WithCache(ctx context.Context, shopIDs []uint64, tagName string) (*seller_seller_tag_core.BatchGetEntityTagResponse, error) {
	result := make(map[uint64]*seller_seller_tag_core.EntityTag)
	var missedShopIDs []uint64

	// Try to get from cache first
	for _, shopID := range shopIDs {
		cacheKey := s.getShopSellerTagCacheKey(shopID, tagName)
		if val, ok := s.entityTagLruCache.Get(ctx, cacheKey); ok {
			result[shopID] = val
			continue
		}
		missedShopIDs = append(missedShopIDs, shopID)
	}

	// If we have any missed shops, fetch them from API
	if len(missedShopIDs) > 0 {
		resp, err := s.GetShopSellerTagV2(ctx, missedShopIDs, tagName)
		if err != nil {
			return nil, err
		}

		// cache the results and add to result map
		for _, entityTag := range resp.GetEntityTags() {
			if entityTag != nil {
				// todo
				shopID := entityTag.GetEntity().GetId()
				cacheKey := s.getShopSellerTagCacheKey(shopID, tagName)
				s.entityTagLruCache.Set(ctx, cacheKey, entityTag)
				result[shopID] = entityTag
			}
		}
	}

	return &seller_seller_tag_core.BatchGetEntityTagResponse{
		EntityTags: collection.MapValues(result),
	}, nil
}

func (s *SellerTagApiImpl) BatchGetShopSellerTags(ctx context.Context, shopIDs []uint64, tagNames []string) ([]*seller_seller_tag_core.EntityTag, error) {
	caller := concurrency.NewConcurrencySplitCaller[uint64, *seller_seller_tag_core.EntityTag]()
	resp, err := caller.Call(ctx, shopIDs, 20, func(ctx context.Context, queries []uint64) ([]*seller_seller_tag_core.EntityTag, error) {
		return s.batchGetShopSellerTags(ctx, queries, tagNames)
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *SellerTagApiImpl) batchGetShopSellerTags(ctx context.Context, shopIDs []uint64, tagNames []string) ([]*seller_seller_tag_core.EntityTag, error) {
	req := &seller_seller_tag_core.BatchGetEntityTagRequest{}
	entityTagIDs := make([]*seller_seller_tag_core.EntityTagIDs, 0)
	for _, shopID := range shopIDs {
		entityTagIDs = append(entityTagIDs, &seller_seller_tag_core.EntityTagIDs{
			Entity: &seller_seller_tag_core.Entity{
				Id:   proto.Uint64(shopID),
				Type: proto.Uint32(uint32(seller_seller_tag_core.Constant_ENTITY_TYPE_SHOP)),
			},
			TagNames: tagNames,
		})
	}
	req.EntityTagsList = entityTagIDs

	entityTags, err := s.SpexClient.BatchGetEntityTag(ctx, req)
	if err != nil {
		return nil, err
	}

	return entityTags, nil
}

func (s *SellerTagApiImpl) BatchGetShopSellerTagsV2(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error) {
	shopIDToTagNames := make(map[uint64][]string)
	for _, query := range queries {
		shopIDToTagNames[query.ShopID] = append(shopIDToTagNames[query.ShopID], query.TagName)
	}

	var entityTagIDs []*seller_seller_tag_core.EntityTagIDs
	for shopID, tagNames := range shopIDToTagNames {
		entityTagIDs = append(entityTagIDs, &seller_seller_tag_core.EntityTagIDs{
			Entity: &seller_seller_tag_core.Entity{
				Id:   proto.Uint64(shopID),
				Type: proto.Uint32(uint32(seller_seller_tag_core.Constant_ENTITY_TYPE_SHOP)),
			},
			TagNames: tagNames,
		})
	}

	caller := concurrency.NewConcurrencySplitCaller[*seller_seller_tag_core.EntityTagIDs, *seller_seller_tag_core.EntityTag]()
	resp, err := caller.Call(ctx, entityTagIDs, 20, s.batchGetShopSellerTagsV2)
	if err != nil {
		return nil, err
	}

	queryToTagValue := make(map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue)
	for _, entityTag := range resp {
		queryToTagValue[entity.SellerTagQuery{
			ShopID:  entityTag.GetEntity().GetId(),
			TagName: strings.ToLower(entityTag.GetTagName()),
		}] = entityTag.GetValue()
	}

	return queryToTagValue, nil
}

func (s *SellerTagApiImpl) batchGetShopSellerTagsV2(ctx context.Context, queries []*seller_seller_tag_core.EntityTagIDs) ([]*seller_seller_tag_core.EntityTag, error) {
	req := &seller_seller_tag_core.BatchGetEntityTagRequest{
		EntityTagsList: queries,
	}
	entityTags, err := s.SpexClient.BatchGetEntityTag(ctx, req)
	if err != nil {
		return nil, err
	}

	return entityTags, nil
}

func (s *SellerTagApiImpl) BatchGetShopSellerTagsV2WithCache(ctx context.Context, queries []entity.SellerTagQuery) (map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue, error) {
	result := make(map[entity.SellerTagQuery]*seller_seller_tag_core.TagValue)
	var missedQueries []entity.SellerTagQuery

	// Try to get from cache first
	for _, query := range queries {
		cacheKey := s.getShopSellerTagV2CacheKey(query)
		if val, ok := s.tagValueLruCache.Get(ctx, cacheKey); ok {
			result[query] = val
			continue
		}
		missedQueries = append(missedQueries, query)
	}

	// If we have any missed queries, fetch them from API
	if len(missedQueries) > 0 {
		resp, err := s.BatchGetShopSellerTagsV2(ctx, missedQueries)
		if err != nil {
			return nil, err
		}

		// cache the results and add to result map
		for query, tagValue := range resp {
			if tagValue != nil {
				cacheKey := s.getShopSellerTagV2CacheKey(query)
				s.tagValueLruCache.Set(ctx, cacheKey, tagValue)
				result[query] = tagValue
			}
		}
	}

	return result, nil
}

func (s *SellerTagApiImpl) getShopSellerTagCacheKey(shopID uint64, tagName string) string {
	return fmt.Sprintf("shop.seller.tag.%s.%d", tagName, shopID)
}

func (s *SellerTagApiImpl) getShopSellerTagV2CacheKey(query entity.SellerTagQuery) string {
	return fmt.Sprintf("shop.seller.tag.v2.%d.%s", query.ShopID, query.TagName)
}
