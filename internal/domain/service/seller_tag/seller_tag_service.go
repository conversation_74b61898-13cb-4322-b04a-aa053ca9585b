package seller_tag

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

type SellerTagService interface {
	GetShopSellerTag(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error)
	GetShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error)
	GetShopIDToChoiceStatus(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error)
	GetShopIDToLocalRetailCBTag(ctx context.Context, shopIDs []uint64) (map[uint64]bool, error)
	BatchGetShopSellerTag(ctx context.Context, shopIDs []uint64, tagNames []string) (map[uint64]map[string]bool, error)
}

type SellerTagServiceImpl struct {
	configAccessor config.ConfAccessor
	sellerTagApi   SellerTagApi
}

func NewSellerTagServiceImpl(configAccessor config.ConfAccessor, sellerTagApi SellerTagApi) *SellerTagServiceImpl {
	return &SellerTagServiceImpl{
		configAccessor: configAccessor,
		sellerTagApi:   sellerTagApi,
	}
}

func (s *SellerTagServiceImpl) GetShopSellerTag(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	result := make(map[uint64]bool)
	resp, err := s.sellerTagApi.GetShopSellerTagV2(ctx, shopIDs, tagName)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get seller tag failed, shopIDs: %s, tagName: %s", Logger.JsonString(shopIDs), tagName)
		return result, err
	}
	for _, entityTag := range resp.GetEntityTags() {
		result[entityTag.GetEntity().GetId()] = entityTag.GetValue().GetBoolV()
	}
	return result, nil
}

func (s *SellerTagServiceImpl) GetShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	return s.getShopSellerTagWithCache(ctx, shopIDs, tagName)
}

func (s *SellerTagServiceImpl) BatchGetShopSellerTag(ctx context.Context, shopIDs []uint64, tagNames []string) (map[uint64]map[string]bool, error) {
	lowerToOriginalTag := make(map[string]string)
	lowerTagNames := make([]string, 0, len(tagNames))
	for _, tagName := range tagNames {
		lowerTagName := strings.ToLower(tagName)
		lowerToOriginalTag[lowerTagName] = tagName
		lowerTagNames = append(lowerTagNames, lowerTagName)
	}

	shopIDToLowerTagToValue := make(map[uint64]map[string]bool)
	var err error
	if s.configAccessor.GetEnableCachedSellerTagFlow(ctx) {
		shopIDToLowerTagToValue, err = s.batchGetShopSellerTagWithCache(ctx, shopIDs, lowerTagNames)
		if err != nil {
			return nil, err
		}
	} else {
		tags, err := s.sellerTagApi.BatchGetShopSellerTags(ctx, shopIDs, lowerTagNames)
		if err != nil {
			return nil, err
		}

		for _, tag := range tags {
			shopID := tag.GetEntity().GetId()
			if _, ok := shopIDToLowerTagToValue[shopID]; !ok {
				shopIDToLowerTagToValue[shopID] = make(map[string]bool)
			}
			shopIDToLowerTagToValue[shopID][strings.ToLower(tag.GetTagName())] = tag.GetValue().GetBoolV()
		}
	}

	shopIDToTagToValue := make(map[uint64]map[string]bool, len(shopIDToLowerTagToValue))
	for shopID, lowerTagToValue := range shopIDToLowerTagToValue {
		shopIDToTagToValue[shopID] = make(map[string]bool, len(lowerTagToValue))
		for lowerTag, value := range lowerTagToValue {
			shopIDToTagToValue[shopID][lowerToOriginalTag[lowerTag]] = value
		}
	}

	return shopIDToTagToValue, nil
}

func (s *SellerTagServiceImpl) batchGetShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagNames []string) (map[uint64]map[string]bool, error) {
	var queries []entity.SellerTagQuery
	for _, shopID := range shopIDs {
		for _, tagName := range tagNames {
			queries = append(queries, entity.SellerTagQuery{
				ShopID:  shopID,
				TagName: strings.ToLower(tagName),
			})
		}
	}

	queryToTagValue, err := s.sellerTagApi.BatchGetShopSellerTagsV2WithCache(ctx, queries)
	if err != nil {
		return nil, err
	}

	result := make(map[uint64]map[string]bool)
	for query, tagValue := range queryToTagValue {
		shopID := query.ShopID
		tagName := query.TagName
		if _, ok := result[shopID]; !ok {
			result[shopID] = make(map[string]bool)
		}
		result[shopID][strings.ToLower(tagName)] = tagValue.GetBoolV()
	}

	return result, nil
}

func (s *SellerTagServiceImpl) GetShopIDToChoiceStatus(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	return s.getShopSellerTagWithCache(ctx, shopIDs, tagName)
}

func (s *SellerTagServiceImpl) GetShopIDToLocalRetailCBTag(ctx context.Context, shopIDs []uint64) (map[uint64]bool, error) {
	return s.getShopSellerTagWithCache(ctx, shopIDs, "Local_Retail_CB")
}

func (s *SellerTagServiceImpl) getShopSellerTagWithCache(ctx context.Context, shopIDs []uint64, tagName string) (map[uint64]bool, error) {
	result := make(map[uint64]bool)
	resp, err := s.sellerTagApi.GetShopSellerTagV2WithCache(ctx, shopIDs, tagName)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get seller tag failed, shopIDs: %s, tagName: %s", Logger.JsonString(shopIDs), tagName)
		return result, err
	}
	for _, entityTag := range resp.GetEntityTags() {
		result[entityTag.GetEntity().GetId()] = entityTag.GetValue().GetBoolV()
	}
	return result, nil
}
