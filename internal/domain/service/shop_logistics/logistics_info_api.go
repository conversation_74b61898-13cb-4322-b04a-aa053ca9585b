package shop_logistics

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_logistics_shop_channels.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/concurrency"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ShopLogisticsInfoApi interface {
	BatchGetShopChannelData(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDisplayChannelData, error)
	GetChannels(ctx context.Context, _ string) ([]*marketplace_logistics_shop_channels.Channel, error)
}

type ShopLogisticsInfoApiImpl struct {
	spexClient       spexlib.SpexClient
	configGetter     config.ConfAccessor
	shopChannelCache lru.Cache[entity.ShopDisplayChannelData]
	channelCache     lru.Cache[[]*marketplace_logistics_shop_channels.Channel]
}

func NewShopLogisticsInfoApiImpl(spexClient spexlib.SpexClient, configGetter config.ConfAccessor) (*ShopLogisticsInfoApiImpl, error) {
	shopChannelCache, err := lru.NewLruCache[entity.ShopDisplayChannelData](cache.ShopChannelLruName)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	channelCache, err := lru.NewLruCache[[]*marketplace_logistics_shop_channels.Channel](cache.ChannelLruName)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	return &ShopLogisticsInfoApiImpl{
		spexClient:       spexClient,
		configGetter:     configGetter,
		shopChannelCache: shopChannelCache,
		channelCache:     channelCache,
	}, nil
}

func (l *ShopLogisticsInfoApiImpl) BatchGetShopChannelData(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDisplayChannelData, error) {
	resp, err := cache.MultiLoadManyFromMap(
		ctx,
		l.shopChannelCache,
		shopIDs,
		l.getShopDisplayChannelDataCacheKey,
		l.batchGetShopChannelData,
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (l *ShopLogisticsInfoApiImpl) batchGetShopChannelData(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDisplayChannelData, error) {
	caller := concurrency.NewConcurrencySplitCaller[uint64, *marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels]()
	resp, err := caller.Call(ctx, shopIDs, l.configGetter.GetShopEnableChannelsAPIBatchSize(ctx), l.batchGetShopChannels)
	if err != nil {
		return nil, err
	}
	shopIDToData := buildShopIDToChannelData(resp)
	result := make(map[uint64]entity.ShopDisplayChannelData)
	for _, sID := range shopIDs {
		result[sID] = shopIDToData[sID]
	}

	return result, nil
}

func (l *ShopLogisticsInfoApiImpl) batchGetShopChannels(ctx context.Context, shopIDs []uint64) ([]*marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels, error) {
	req := &marketplace_logistics_shop_channels.BatchGetShopChannelsRequest{}

	for _, shopID := range shopIDs {
		req.ShopIdList = append(req.ShopIdList, int64(shopID))
	}

	shopLogisticsChannels, err := l.spexClient.BatchGetShopChannels(ctx, req)
	if err != nil {
		return nil, err
	}

	return shopLogisticsChannels, nil
}

func (l *ShopLogisticsInfoApiImpl) getShopDisplayChannelDataCacheKey(shopID uint64) string {
	return fmt.Sprintf("shop.display.channel.data.%d", shopID)
}

func (l *ShopLogisticsInfoApiImpl) GetChannels(ctx context.Context, _ string) ([]*marketplace_logistics_shop_channels.Channel, error) {
	res, err := cache.LoadSingle[string, []*marketplace_logistics_shop_channels.Channel](
		ctx,
		l.channelCache,
		envvar.GetCID(ctx),
		l.getRegionChannelsCacheKey,
		l.getChannels,
	)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (l *ShopLogisticsInfoApiImpl) getRegionChannelsCacheKey(region string) string {
	return "shop.region.channels." + region
}

func (l *ShopLogisticsInfoApiImpl) getChannels(ctx context.Context, _ string) ([]*marketplace_logistics_shop_channels.Channel, error) {
	req := &marketplace_logistics_shop_channels.GetChannelsRequest{}
	channelList, err := l.spexClient.GetChannels(ctx, req)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}
	return channelList, nil
}

type WarehouseLogisticsInfoApi interface {
	RegionAllWarehouseDisplayChannelData(ctx context.Context, warehouseCodes []string) map[string]entity.WarehouseDisplayChannelData
}

type WarehouseLogisticsInfoApiImpl struct {
	confAccessor               config.ConfAccessor
	warehouseChannelLocalCache localcache.LocalCache[string, entity.WarehouseDisplayChannelData]
}

func NewWarehouseLogisticsInfoApiImpl(
	confAccessor config.ConfAccessor,
) (*WarehouseLogisticsInfoApiImpl, error) {
	warehouseChannelLocalCache, err := localcache.GetCache[string, entity.WarehouseDisplayChannelData](cache.WarehouseChannels)
	if err != nil {
		return nil, err
	}
	return &WarehouseLogisticsInfoApiImpl{
		confAccessor:               confAccessor,
		warehouseChannelLocalCache: warehouseChannelLocalCache,
	}, nil
}

func (w *WarehouseLogisticsInfoApiImpl) RegionAllWarehouseDisplayChannelData(ctx context.Context, warehouseCodes []string) map[string]entity.WarehouseDisplayChannelData {
	return w.warehouseChannelLocalCache.MGet(ctx, warehouseCodes)
}
