package shop_logistics

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
)

type ShopLogisticsService interface {
	GenerateShopIDToEnabledChannelIDs(ctx context.Context, shopIDs []uint64, fulfillmentInfo entity.FulfillmentInfo) (map[uint64][]int, error)
	GetChannelIdToChannel(ctx context.Context) (map[uint64]entity.ShopChannel, error)
}

type ShopLogisticsServiceImpl struct {
	shopLogisticsApi      ShopLogisticsInfoApi
	warehouseLogisticsAPI WarehouseLogisticsInfoApi
}

func NewShopLogisticsServiceImpl(shopLogisticsApi ShopLogisticsInfoApi, warehouseLogisticsAPI WarehouseLogisticsInfoApi) *ShopLogisticsServiceImpl {
	return &ShopLogisticsServiceImpl{shopLogisticsApi: shopLogisticsApi, warehouseLogisticsAPI: warehouseLogisticsAPI}
}

func (s *ShopLogisticsServiceImpl) ShopEnabledChannelIDs(ctx context.Context, shopIDs []uint64) (map[uint64][]int, error) {
	resp, err := s.shopLogisticsApi.BatchGetShopChannelData(ctx, shopIDs)
	if err != nil {
		return nil, err
	}

	result := make(map[uint64][]int)
	for _, sID := range shopIDs {
		result[sID] = resp[sID].Enabled
	}

	return result, nil
}

func (s *ShopLogisticsServiceImpl) GenerateShopIDToEnabledChannelIDs(ctx context.Context, shopIDs []uint64, fulfillmentInfo entity.FulfillmentInfo) (map[uint64][]int, error) {
	shopIDToChannelIDs := make(map[uint64][]int)

	if fulfillmentInfo.ManagedBySBS {
		// the whole shipping order should use warehouse address
		warehouseCode := fulfillmentInfo.WarehouseCode
		warehouseLevelEnabledChannelIDs, err := s.WarehouseEnabledChannelIDs(ctx, []string{warehouseCode})
		if err != nil {
			return nil, fmt.Errorf("LogisticsInfo.getShopIDToEnabledChannelIDs|shop_enabled_channel_err=%v", err)
		}
		if _, ok := warehouseLevelEnabledChannelIDs[warehouseCode]; !ok {
			return nil, fmt.Errorf("LogisticsInfo.getShopIDToEnabledChannelIDs|err=no_channel_for_%s", warehouseCode)
		}
		for _, shopID := range shopIDs {
			shopIDToChannelIDs[shopID] = warehouseLevelEnabledChannelIDs[warehouseCode]
		}
	} else {
		var err error
		shopIDToChannelIDs, err = s.ShopEnabledChannelIDs(ctx, shopIDs)
		if err != nil {
			return nil, fmt.Errorf("LogisticsInfo.getShopIDToEnabledChannelIDs|shop_enabled_channel_err=%v", err)
		}
	}
	return shopIDToChannelIDs, nil
}

func (s *ShopLogisticsServiceImpl) WarehouseEnabledChannelIDs(ctx context.Context, warehouseCodes []string) (map[string][]int, error) {
	resp := s.warehouseLogisticsAPI.RegionAllWarehouseDisplayChannelData(ctx, warehouseCodes)

	res := make(map[string][]int)
	for _, wID := range warehouseCodes {
		res[wID] = resp[wID].Enabled
	}

	return res, nil
}

func (s *ShopLogisticsServiceImpl) GetChannelIdToChannel(ctx context.Context) (map[uint64]entity.ShopChannel, error) {
	rawChannels, err := s.shopLogisticsApi.GetChannels(ctx, "")
	if err != nil {
		return nil, err
	}

	channelsMap := make(map[uint64]entity.ShopChannel, len(rawChannels))
	for _, rawChannel := range rawChannels {
		channel := buildShopChannel(rawChannel)
		channelsMap[channel.ChannelId] = channel
	}

	return channelsMap, nil
}
