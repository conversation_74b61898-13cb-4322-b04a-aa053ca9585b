package soc

//go:generate mockgen --build_flags=--mod=mod -source=service.go -destination=service_mock.go -package=soc -mock_names=SOCService=MockSOCService

import (
	"context"
	"errors"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
)

type SOCService interface {
	BatchCheckSOCStationIDServiceability(
		ctx context.Context, socLocations []string, buyerAddress entity.ItemGroupBuyerAddress,
	) (map[string]bool, error)
}

type SOCServiceImpl struct {
	socVersionCache mixed_cache.LruLayerCache[string]
	socServiceCache mixed_cache.LruLayerCache[bool]
}

func NewSOCService(clients redishelper.GlobalRedisClients) (*SOCServiceImpl, error) {
	socServiceCache, err := newSocServiceCache(clients)
	if err != nil {
		return nil, err
	}

	socVersionCache, err := newSocVersionCache(clients)
	if err != nil {
		return nil, err
	}
	return &SOCServiceImpl{
		socVersionCache: socVersionCache,
		socServiceCache: socServiceCache,
	}, nil
}

func newSocVersionCache(clients redishelper.GlobalRedisClients) (mixed_cache.LruLayerCache[string], error) {
	client, err := clients.GetRedisClusterByClusterName(redishelper.Static)
	if err != nil {
		return nil, err
	}
	socVersionCache, err := mixed_cache.NewStrLruLayerCache[string](
		cache.SocVersionCacheName,
		client,
		[]lru.InitOptions{},
		[]remote_cache.InitOptions[string]{
			remote_cache.WithoutNamespace[string](),
		},
	)
	if err != nil {
		return nil, err
	}
	return socVersionCache, nil
}

func newSocServiceCache(clients redishelper.GlobalRedisClients) (mixed_cache.LruLayerCache[bool], error) {
	client, err := clients.GetRedisClusterByClusterName(redishelper.Static)
	if err != nil {
		return nil, err
	}
	socServiceCache, err := mixed_cache.NewBasicLruLayerCache[bool](
		cache.SoCServiceabilityCacheName,
		client,
		[]lru.InitOptions{},
		[]remote_cache.InitOptions[bool]{
			remote_cache.WithoutNamespace[bool](),
		},
	)
	if err != nil {
		return nil, err
	}
	return socServiceCache, nil
}

func (s *SOCServiceImpl) BatchCheckSOCStationIDServiceability(
	ctx context.Context, socLocations []string, buyerAddress entity.ItemGroupBuyerAddress,
) (map[string]bool, error) {
	if buyerAddress.State == "" && buyerAddress.City == "" && buyerAddress.District == "" {
		Logger.CtxLogInfof(ctx, "buyer address is not available, skip SOC check")
		return make(map[string]bool), nil
	}

	versionID, ok := cache.Get(
		ctx,
		s.socVersionCache,
		envvar.GetCID(ctx),
		getVersionIDCacheKey,
	)
	if !ok {
		return nil, errors.New("failed to get soc version")
	}

	socStationIDToServiceability := cache.MultiGetMany(
		ctx,
		s.socServiceCache,
		socLocations,
		func(id string) string {
			return getRegionSOCServiceabilityInfoCacheKey(versionID, id, buyerAddress.State, buyerAddress.City, buyerAddress.District)
		},
		multi_layer_cache.WithLocalOptions[lru.Option, remote_cache.Option](lru.WithTimeout(time.Minute)),
	)

	return socStationIDToServiceability, nil
}

func getRegionSOCServiceabilityInfoCacheKey(
	versionID,
	socStationID,
	state,
	city,
	district string,
) string {
	return strings.ToUpper(
		strings.Join([]string{
			"site_serviceable_area_location",
			versionID,
			socStationID,
			state,
			city,
			district,
		}, ":"),
	)
}

func getVersionIDCacheKey(region string) string {
	return strings.ToUpper("site_serviceable_area_location_version:" + region)
}
