package lpslib

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/httplib"
)

var (
	ShoppingCartValidation = httplib.HttpService{
		Endpoint: "/api/v3/logistics/shopping_cart_validation",
		Scene:    constant.MultiAlive,
		System:   constant.SystemLPS,
	}

	GetAllChannel = httplib.HttpService{
		Endpoint: "/api/v3/logistics/channels/get/",
		Scene:    constant.MultiAlive,
		System:   constant.SystemLPS,
	}
)

type LpsClient interface {
	ShoppingCartValidation(ctx context.Context, req *GetAllChannelsWeightDimReq) (*GetAllChannelsWeightDimResp, error)
	GetAllChannel(ctx context.Context, region meta.Region) ([]InfoWithExtraData, error)
}

type LpsClientImpl struct {
	client       httplib.Client
	confAccessor config.ConfAccessor
}

func NewLpsClientImpl(confAccessor config.ConfAccessor) *LpsClientImpl {
	return &LpsClientImpl{
		client:       httplib.NewBasicClient(confAccessor, constant.ServiceLpsApi),
		confAccessor: confAccessor,
	}
}

func (s *LpsClientImpl) GetAllChannel(ctx context.Context, region meta.Region) ([]InfoWithExtraData, error) {
	req := &GetAllChannelsReq{
		Token:   s.token(ctx),
		Country: string(region),
	}
	resp := &GetAllChannelsResp{}
	err := s.client.Post(ctx, constant.ServiceLpsApi, GetAllChannel, req, resp, s.header(ctx), httplib.WithRegion(region))
	if err != nil {
		return nil, err
	}
	return resp.Data.Channels, nil
}

func (s *LpsClientImpl) ShoppingCartValidation(ctx context.Context, req *GetAllChannelsWeightDimReq) (*GetAllChannelsWeightDimResp, error) {
	resp := &GetAllChannelsWeightDimResp{}
	err := s.client.Post(ctx, constant.ServiceLpsApi, ShoppingCartValidation, req, resp, s.header(ctx))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *LpsClientImpl) header(ctx context.Context) httplib.Header {
	return newHeader(s.token(ctx))
}

func (s *LpsClientImpl) token(ctx context.Context) string {
	return s.confAccessor.GetLPSAPIToken(ctx)
}
