package lpslib

import (
	"context"

	"github.com/dolthub/swiss"
	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func DumpChannels(ctx context.Context, confAccessor config.ConfAccessor) (*swiss.Map[int, *ChannelInfo], error) {
	client := NewLpsClientImpl(confAccessor)
	channels, err := client.GetAllChannel(ctx, envvar.GetRegion(ctx))
	if err != nil {
		return nil, err
	}
	ret := swiss.NewMap[int, *ChannelInfo](uint32(len(channels)))
	channelsWithSelectableDeliveryTime := collection.NewSetFromSlice(confAccessor.GetChannelsWithSelectableDeliveryTime(ctx))
	for _, channel := range channels {
		channelInfo := &ChannelInfo{}
		if len(channel.ExtraData) == 0 {
			channel.ExtraData = "{}"
		}
		extraInfo := ChannelExtraInfo{}
		if err = jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal([]byte(channel.ExtraData), &extraInfo); err != nil {
			return nil, fsserr.With(fsserr.JsonErr, err)
		}
		hasSelectableDeliveryTime := channelsWithSelectableDeliveryTime.Contains(channel.ChannelID)
		channel.ChannelInfo.ExtraInfo = extraInfo
		channel.ChannelInfo.HasSelectableDeliveryTime = hasSelectableDeliveryTime
		channel.ChannelInfo.ATLEligible = channelSupportATL(channel.AvailableDeliveryInstruction)
		ret.Put(channel.ChannelID, channelInfo)
	}
	return ret, nil
}

func channelSupportATL(availableDeliveryInstructions []AvailableDeliveryInstruction) bool {
	for _, instruction := range availableDeliveryInstructions {
		if instruction.Category == constant.DeLiveryMethodCategory {
			for _, info := range instruction.DeliveryInstructionInfo {
				if info == constant.AtlDeliveryInstructionInfo {
					return true
				}
			}
		}
	}
	return false
}
