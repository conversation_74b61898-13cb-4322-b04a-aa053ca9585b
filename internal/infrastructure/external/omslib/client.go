package omslib

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/httplib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache/mixed_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	BatchGetWhsPriority = httplib.HttpService{
		Endpoint: "/whs_api/batch_priority",
		Scene:    constant.MultiAlive,
		System:   constant.SystemOMS,
	}
)

type OMSClient interface {
	GetAllWarehousePriority(ctx context.Context, req *WhsPriorityBatchRequest) ([]WhsPriorityEntity, error)
	GetAllWarehousePriorityUsingCache(ctx context.Context, req *WhsPriorityBatchRequest) ([]WhsPriorityEntity, error)
}

func NewOMSClientImpl(confAccessor config.ConfAccessor, clients redishelper.GlobalRedisClients) (*OmsClientImpl, error) {
	warehousePriorityCache, err := mixed_cache.NewLruLayerCacheWithRedis[[]WhsPriorityEntity](clients, cache.WarehousePriorityCacheName, redishelper.Default, nil, nil)
	if err != nil {
		// todo: handle error properly
		return nil, err
	}
	client := &OmsClientImpl{
		confAccessor:           confAccessor,
		client:                 httplib.NewBasicClient(confAccessor, constant.ServiceOmsApi),
		warehousePriorityCache: warehousePriorityCache,
	}
	return client, nil
}

type OmsClientImpl struct {
	confAccessor           config.ConfAccessor
	client                 httplib.Client
	warehousePriorityCache mixed_cache.LruLayerCache[[]WhsPriorityEntity]
}

func (o *OmsClientImpl) GetAllWarehousePriority(ctx context.Context, req *WhsPriorityBatchRequest) ([]WhsPriorityEntity, error) {
	resp := &WhsPriorityBatchResponse{}
	err := o.client.Get(ctx, constant.ServiceOmsApi, BatchGetWhsPriority, req, resp, o.header(ctx))
	if err != nil {
		return nil, fsserr.With(fsserr.OmsError, err)
	}
	return resp.Data, nil
}

func (o *OmsClientImpl) GetAllWarehousePriorityUsingCache(ctx context.Context, req *WhsPriorityBatchRequest) ([]WhsPriorityEntity, error) {
	ret, err := cache.LoadSingle(
		ctx,
		o.warehousePriorityCache,
		req,
		batchGetAllWarehousePriorityCacheKey,
		o.GetAllWarehousePriority,
	)
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (o *OmsClientImpl) header(ctx context.Context) *omsHeader {
	return newHeader(o.confAccessor.GetOmsApiSecret(ctx))
}

func batchGetAllWarehousePriorityCacheKey(req *WhsPriorityBatchRequest) string {
	state := strings.ToLower(strings.TrimSpace(req.State))
	city := strings.ToLower(strings.TrimSpace(req.City))
	data := []string{req.Country, state, city}
	if req.District != "" {
		data = append(data, strings.ToLower(strings.TrimSpace(req.District)))
	}
	data = append(data, strconv.FormatUint(req.ShopID, 10))
	return strings.Join(data, ":")
}
