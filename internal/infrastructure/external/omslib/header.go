package omslib

import (
	"context"

	"github.com/dgrijalva/jwt-go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
)

type omsHeader struct {
	secret string
}

func newHeader(secret string) *omsHeader {
	return &omsHeader{
		secret: secret,
	}
}

func (s *omsHeader) Header(ctx context.Context) (map[string]string, error) {
	token, err := s.generateToken(ctx)
	if err != nil {
		return nil, err
	}
	return map[string]string{"jwt-token": token}, nil
}

func (s *omsHeader) generateToken(ctx context.Context) (string, error) {
	now := timeutil.Now(ctx).Unix()
	token := jwt.NewWithClaims(
		jwt.SigningMethodHS256, jwt.MapClaims{
			"typ": now,
			"exp": now + 60,
		},
	)
	token.Header["optr"] = "sls"
	// Sign and get the complete encoded token as a string using the secret
	tokenString, err := token.SignedString([]byte(s.secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
