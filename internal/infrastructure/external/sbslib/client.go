package sbslib

import (
	"context"
	"net/url"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/httplib"
)

var (
	GetAllChannelByRegion = httplib.HttpService{
		Endpoint: "/api/openapi/whschannel/get_all_channel_by_region",
		Scene:    constant.MultiAlive,
		System:   constant.SystemSBS,
	}
)

type SbsClient interface {
	GetAllWarehouseChannelByRegion(ctx context.Context) ([]SBSGetAllRegionData, error)
}

type SbsClientImpl struct {
	client       httplib.Client
	confAccessor config.ConfAccessor
}

func NewSbsClientImpl(confAccessor config.ConfAccessor) *SbsClientImpl {
	return &SbsClientImpl{
		client:       httplib.NewBasicClient(confAccessor, constant.ServiceSBSApi),
		confAccessor: confAccessor,
	}
}

func (s *SbsClientImpl) GetAllWarehouseChannelByRegion(ctx context.Context) ([]SBSGetAllRegionData, error) {
	region := envvar.GetRegion(ctx)
	req := &GetAllChannelByRegionRequest{
		Region: region.Lower(),
	}
	resp := &SBSGetAllRegionResponse{}
	err := s.client.Get(ctx, constant.ServiceSBSApi, GetAllChannelByRegion, req, resp, s.header(ctx, region, nil))
	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

func (s *SbsClientImpl) header(ctx context.Context, region meta.Region, urlVals url.Values) httplib.Header {
	if urlVals == nil {
		urlVals = make(url.Values)
	}
	urlVals.Set("region", region.Lower())
	host, _ := s.client.GetBaseHost(ctx, region, constant.ServiceSBSApi)
	return newSbsHeader(
		s.confAccessor.GetSBSApiKey(ctx),
		s.confAccessor.GetSBSApiSecret(ctx),
		host,
		urlVals,
	)
}
