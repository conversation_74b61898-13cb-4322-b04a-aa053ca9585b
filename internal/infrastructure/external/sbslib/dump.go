package sbslib

import (
	"context"
	"strconv"

	"github.com/dolthub/swiss"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

func DumpWarehouseChannels(ctx context.Context, confAccessor config.ConfAccessor) (*swiss.Map[string, entity.WarehouseDisplayChannelData], error) {
	client := NewSbsClientImpl(confAccessor)
	sbsAllRegions, err := client.GetAllWarehouseChannelByRegion(ctx)
	if err != nil {
		return nil, err
	}

	if len(sbsAllRegions) != 1 {
		return nil, fsserr.New(fsserr.SbsError, "|data length="+strconv.Itoa(len(sbsAllRegions)))
	}

	sbsAllRegion := sbsAllRegions[0]

	ret := swiss.NewMap[string, entity.WarehouseDisplayChannelData](uint32(len(sbsAllRegion.WhsList)))
	for _, warehouse := range sbsAllRegion.WhsList {
		warehouseData := entity.WarehouseDisplayChannelData{}
		for _, channel := range warehouse.ChannelList {
			if channel.ChannelStatus == 1 {
				warehouseData.Enabled = append(warehouseData.Enabled, channel.ChannelId)
			}
			if channel.ChannelCodStatus == 1 {
				warehouseData.CodEnabled = append(warehouseData.CodEnabled, channel.ChannelId)
			}
		}
		ret.Put(warehouse.WhsId, warehouseData)
	}
	return ret, nil
}
