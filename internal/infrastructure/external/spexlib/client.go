package spexlib

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_address.pb"
	account_core64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_core.pb"
	location_user_location64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/location_user_location.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_item_itemaggregation_iteminfo.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_itemtagservice_querying_api.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_logistics_shop_channels.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_cb_collection_api.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_fulfilment_sbs.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_address_core.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_tag_core.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shop_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/layer_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type PriceSpexClient interface {
	GetCheckoutStockInfo(ctx context.Context, request *price_checkout_promo.GetCheckoutStockInfoRequest) ([]*price_checkout_promo.CheckoutOrderStockInfo, fsserr.Error)
}

type AccountSpexClient interface {
	GetAccountDetail(ctx context.Context, userId typ.UserIdType, region meta.Region) (*account_core64.UserDetail, fsserr.Error)
	GetPrivateAddressWithGeocoding(ctx context.Context, userId typ.UserIdType, addressID uint64) (*account_address.PrivateAddressGeoCoded, fsserr.Error)
	BatchGetPrivateAddressWithGeocoding(ctx context.Context, userId typ.UserIdType, addressIDs []uint64) (map[uint64]*account_address.PrivateAddressGeoCoded, fsserr.Error)
}

type LocationSpexClient interface {
	GetShippingAddresses(ctx context.Context, req *location_user_location64.GetShippingAddressCombinationsRequest) (*location_user_location64.GetShippingAddressCombinationsResponse, fsserr.Error)
}

type SellerSpexClient interface {
	BatchGetEntityTagValue(ctx context.Context, req *seller_seller_tag_core.BatchGetEntityTagValueRequest) ([]*seller_seller_tag_core.EntityTag, fsserr.Error)
	BatchGetEntityTag(ctx context.Context, req *seller_seller_tag_core.BatchGetEntityTagRequest) ([]*seller_seller_tag_core.EntityTag, fsserr.Error)
	BatchGetWarehouseFlagByShop(ctx context.Context, shopIds []int64) ([]*seller_seller_address_core.ShopWarehouseFlag, fsserr.Error)
	BatchGetWarehouseByShopWithoutPagination(ctx context.Context, shopIds []int64) ([]*seller_seller_address_core.ShopWarehouse, fsserr.Error)
}

type ShopSpexClient interface {
	BatchGetShopSipBasic(ctx context.Context, shopIds []int64) ([]*shop_core.SipBasic, fsserr.Error)
	GetShopBatch(ctx context.Context, shopIds []int64) ([]*shop_core.ShopDetail, fsserr.Error)
	BatchCheckSellerWarehouseShop(ctx context.Context, shopIds []int64) (*shop_core.BatchCheckSellerWarehouseShopResponse, fsserr.Error)
	BatchGetShopWarehousesByShopIds(ctx context.Context, shopIds []int64) ([]*shop_core.ShopWarehouse, fsserr.Error)
}

type MarketplaceSpexClient interface {
	GetDummyBuyerId(ctx context.Context, cid string) (*marketplace_order_processing_cb_collection_api.GetDummyBuyerIdResponse, fsserr.Error)
	GetItemLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ItemLabel, fsserr.Error)
	GetModelLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetModelLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ModelLabel, fsserr.Error)
	GetSbsItemInfo(ctx context.Context, shopItems []*marketplace_order_processing_fulfilment_sbs.ItemParam) ([]*marketplace_order_processing_fulfilment_sbs.SbsShopItemInfo, fsserr.Error)
	GetSBSShipmentGroups(ctx context.Context, groupIDs []uint32) ([]*marketplace_order_processing_fulfilment_sbs.SbsShipmentGroupInfoDetails, fsserr.Error)
	BatchGetShopChannels(ctx context.Context, req *marketplace_logistics_shop_channels.BatchGetShopChannelsRequest) ([]*marketplace_logistics_shop_channels.ShopDisplayLogisticsChannels, error)
	GetChannels(ctx context.Context, req *marketplace_logistics_shop_channels.GetChannelsRequest) ([]*marketplace_logistics_shop_channels.Channel, error)
	GetProductInfo(ctx context.Context, req *marketplace_listing_item_itemaggregation_iteminfo.GetProductInfoRequest) ([]*marketplace_listing_item_itemaggregation_iteminfo.ProductInfo, error)
}

type SpexClient interface {
	PriceSpexClient
	AccountSpexClient
	LocationSpexClient
	SellerSpexClient
	ShopSpexClient
	MarketplaceSpexClient
}

func NewSpexClientImpl(confAccessor config.ConfAccessor, cacheManager *layer_cache.LayerCacheManager) *SpexClientImpl {
	return &SpexClientImpl{
		ConfAccessor:      confAccessor,
		LayerCacheManager: cacheManager,
	}
}

type SpexClientImpl struct {
	ConfAccessor      config.ConfAccessor
	LayerCacheManager *layer_cache.LayerCacheManager // 缓存管理器（内置异步写入能力）
}

func newReference(service spexService, spexError *spexError) fsserr.Reference {
	if spexError == nil {
		return fsserr.Reference{}
	}
	return fsserr.Reference{
		Source:        service.System,
		SourceCode:    strconv.Itoa(int(spexError.Code())),
		SourceMessage: spexError.Error(),
	}
}
