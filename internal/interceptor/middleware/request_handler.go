package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net"
	"net/http"
	"runtime/debug"
	"time"

	"git.garena.com/shopee/bg-logistics/go/chassis/core/handler"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	uuid "github.com/satori/go.uuid"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/chassis_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/randutil"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

const RequestContextHandlerName = "request-context"

type RequestContextHandler struct {
	ConfAccessor config.ConfAccessor
}

func RegisterRequestContextHandler(confAccessor config.ConfAccessor) {
	requestContextHandlerInit := func() handler.Handler {
		return NewRequestContextHandler(confAccessor)
	}
	_ = handler.RegisterHandler(RequestContextHandlerName, requestContextHandlerInit)
}

func NewRequestContextHandler(confAccessor config.ConfAccessor) handler.Handler {
	return &RequestContextHandler{
		ConfAccessor: confAccessor,
	}
}

func (th *RequestContextHandler) Name() string {
	return RequestContextHandlerName
}

func (th *RequestContextHandler) Handle(i *invocation.Invocation) {
	var (
		ok   bool
		req  *restful.Request
		resp *restful.Response
		err  error
	)
	req, ok = i.Args.(*restful.Request)
	if !ok {
		return
	}

	ctx := i.Ctx
	var (
		requestIP                        = getRequestIp(ctx, req)
		sourceRequestId, serverRequestId = genRequestID(ctx, req)
		requestTypeMark                  = common.GetReqTypeMakerFromXRequestId(ctx, sourceRequestId)
		logHit                           = req.Request.Header.Get(constant.LogHitKey) != ""
		path                             = common.FormatUrl(req.Request.URL.Path)
	)
	ctx = common.InitRequestContext(ctx, th.ConfAccessor, serverRequestId, logHit, requestTypeMark, path)
	i.Ctx = ctx

	var logTraceNum uint32
	ctx, logTraceNum = ctxutils.SetLogTraceNum(ctx)
	if logTraceNum > 0 {
		// set log trace info
		traceEntity := ctxutils.TraceEntity{
			LogTrace:  logTraceNum,
			Name:      req.Request.URL.Path,
			FromCache: false,
			Error:     "",
		}
		ctx = ctxutils.SetLogTraceInfo(ctx, traceEntity)
	}

	req, body, err := getRequestBody(req)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "middleware logger, getRequestBody error: %v", err)
	}
	resp, _ = i.Reply.(*restful.Response)
	c := newResponseCapture(resp.ResponseWriter, serverRequestId)
	resp.ResponseWriter = c

	start := time.Now()
	defer func() {
		if panicErr := recover(); panicErr != nil {
			Logger.CtxLogErrorf(ctx, "%v|%s|%s| %s - %s|service panic.go|request: %q",
				start.Format("2006/01/02 - 15:04:05"),
				sourceRequestId,
				requestIP,
				req.Request.Method,
				req.Request.URL.RequestURI(),
				compactBody(body),
			)
			Logger.CtxLogErrorf(ctx, "panic.go stack trace: \n%s", debug.Stack())
			_ = monitor.AwesomeReportEvent(ctx, constant.CatModulePanic, req.Request.URL.RequestURI(), monitor.StatusPanic, "")
			panic(panicErr)
		}
	}()

	i.Next()
	end := time.Now()
	latency := end.Sub(start)

	ctx = i.Ctx
	if logTraceNum > 0 {
		traceInfoList := ctxutils.GetLogTraceInfo(ctx)
		if traceInfoList != nil {
			traceInfoListV2 := make([]ctxutils.TraceEntity, 0, len(*traceInfoList))
			for index, traceInfo := range *traceInfoList {
				// time consume to replace with url
				if index == 0 {
					traceInfo.Cost = latency.String()
				}
				traceInfoListV2 = append(traceInfoListV2, traceInfo)
			}

			// todo solve the problem of large number of logs and large storage space
			Logger.CtxLogInfof(ctx, "log_trace [request] length:%d, log_trace_info:%s", len(*traceInfoList), Logger.JsonString(traceInfoListV2))
		}
	}

	if chassis_config.IsInfoEnabled() {
		request := compactBody(body)
		response := compactBody(c.Bytes())
		Logger.CtxLogInfof(ctx, "%v|%v|%s|%s| %s - %s|request: %q|response: %d - %s",
			end.Format("2006/01/02 - 15:04:05"),
			latency,
			serverRequestId,
			requestIP,
			req.Request.Method,
			req.Request.URL.RequestURI(),
			request,
			resp.StatusCode(),
			response,
		)
	}
}

func getRequestIp(ctx context.Context, req *restful.Request) string {
	requestIp := req.HeaderParameter(constant.HeaderRequestIP)
	if requestIp == "" {
		ip, _, err := net.SplitHostPort(req.Request.RemoteAddr)
		if err != nil {
			Logger.CtxLogErrorf(ctx, "split ip failed,RemoteAddr:%s", req.Request.RemoteAddr)
		} else {
			requestIp = ip
		}
	}
	return requestIp
}

func genRequestID(ctx context.Context, req *restful.Request) (string, string) {
	source := req.HeaderParameter(constant.HeaderRequestID)
	if source == "" {
		source = uuid.NewV4().String()
		req.Request.Header.Set(constant.HeaderRequestID, source)
		return source, "|" + source + "|"
	}
	req.Request.Header.Set(constant.HeaderRequestID, source)
	return source, "|" + source + "|" + randutil.RandString(ctx, 4)
}

type responseCapture struct {
	http.ResponseWriter
	wroteHeader bool
	status      int
	body        *bytes.Buffer
}

func newResponseCapture(w http.ResponseWriter, requestId string) *responseCapture {
	w.Header().Set("X-REQUEST-ID", requestId)
	return &responseCapture{
		ResponseWriter: w,
		wroteHeader:    false,
		body:           new(bytes.Buffer),
	}
}

func (c *responseCapture) Header() http.Header {
	return c.ResponseWriter.Header()
}

func (c *responseCapture) Write(data []byte) (int, error) {
	if !c.wroteHeader {
		c.WriteHeader(http.StatusOK)
	}
	c.body.Write(data)
	return c.ResponseWriter.Write(data)
}

func (c *responseCapture) WriteHeader(statusCode int) {
	c.status = statusCode
	c.wroteHeader = true
	c.ResponseWriter.WriteHeader(statusCode)
}

func (c *responseCapture) Bytes() []byte {
	return c.body.Bytes()
}

func (c *responseCapture) StatusCode() int {
	return c.status
}

func getRequestBody(req *restful.Request) (*restful.Request, []byte, error) {
	r := req.Request
	body, bodyErr := io.ReadAll(r.Body)
	if bodyErr != nil {
		return req, nil, bodyErr
	}

	dup := make([]byte, len(body))
	copy(dup, body)
	r.Body = io.NopCloser(bytes.NewBuffer(body))
	return req, dup, nil
}

func compactBody(body []byte) string {
	buf := new(bytes.Buffer)
	if err := json.Compact(buf, body); err != nil {
		return string(body)
	}
	return buf.String()
}
