package handler

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"

	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"

	appservice "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/service"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/mocker"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/api"
)

type ItemGroupingHandler struct {
	itemGroupingService appservice.ItemGroupingService
}

func NewItemGroupingHandler(svc appservice.ItemGroupingService) *ItemGroupingHandler {
	return &ItemGroupingHandler{
		itemGroupingService: svc,
	}
}

func (h *ItemGroupingHandler) URLPatterns() []restful.Route {
	routerGroup := restful.NewRouterGroup("/api/fulfillment_planning")
	routerGroup.POST("/group_items", h.GroupItems)
	routerGroup.POST("/batch_get_seller_wh_priority", h.BatchGetSellerWhPriority)
	routerGroup.POST("/get_eligible_fulfilment_source", h.GetEligibleFulfilmentSource)

	return routerGroup.GetRouters()
}

func (h *ItemGroupingHandler) GroupItems(reqCtx *restful.Context) {
	var (
		pbReq  fss_proto.GroupItemsRequest
		pbResp = &fss_proto.GroupItemsResponse{
			RespHeader: api.NewRespHeader(),
		}
	)
	if err := api.ProtoSchemaParseValidate(reqCtx, &pbReq); err != nil {
		api.WritePBResp(reqCtx, pbResp, err)
		return
	}

	ctx := mocker.MockContext(reqCtx)
	shippingGroups, err := h.itemGroupingService.GroupItemsUseCase(ctx, &pbReq)
	if err != nil {
		api.WritePBResp(reqCtx, pbResp, err)
		return
	}

	pbResp.ShippingGroups = shippingGroups
	api.WritePBResp(reqCtx, pbResp, nil)
}

func (h *ItemGroupingHandler) BatchGetSellerWhPriority(reqCtx *restful.Context) {
	var (
		pbReq  fss_proto.BatchGetSellerWhPriorityRequest
		pbResp = &fss_proto.BatchGetSellerWhPriorityResponse{
			RespHeader: api.NewRespHeader(),
		}
	)
	if err := api.ProtoSchemaParseValidate(reqCtx, &pbReq); err != nil {
		api.WritePBResp(reqCtx, pbResp, err)
		return
	}

	ctx := mocker.MockContext(reqCtx)
	resp, err := h.itemGroupingService.BatchGetSellerWhPriorityUseCase(ctx, &pbReq)
	if err != nil {
		api.WritePBResp(reqCtx, pbResp, err)
		return
	}

	// 将返回的 SellerWhPriority 列表转换为响应
	pbResp.Priorities = resp
	api.WritePBResp(reqCtx, pbResp, nil)
}

func (h *ItemGroupingHandler) GetEligibleFulfilmentSource(reqCtx *restful.Context) {
	var (
		pbReq  fss_proto.GetEligibleFulfilmentSourceRequest
		pbResp = &fss_proto.GetEligibleFulfilmentSourceResponse{
			RespHeader: api.NewRespHeader(),
		}
	)
	if err := api.ProtoSchemaParseValidate(reqCtx, &pbReq); err != nil {
		api.WritePBResp(reqCtx, pbResp, err)
		return
	}

	ctx := mocker.MockContext(reqCtx)
	resp, err := h.itemGroupingService.GetEligibleFulfilmentSourceUseCase(ctx, &pbReq)
	if err != nil {
		api.WritePBResp(reqCtx, pbResp, err)
		return
	}

	// 将返回的 ResponseItemInfo 列表转换为响应
	pbResp.ResponseItemInfo = resp
	api.WritePBResp(reqCtx, pbResp, nil)
}
