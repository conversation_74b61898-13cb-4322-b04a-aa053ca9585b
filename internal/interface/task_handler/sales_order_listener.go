package task_handler

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/order_message_bus.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shared_service_common_message.pb"
	"git.garena.com/shopee/bg-logistics/service/saturn-rpc-job/go/lib/entity"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/api"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type SalesOrderListener struct {
	salesOrdersCountService order.SalesOrdersCountService
}

func NewSalesOrderListener(salesOrdersCountService order.SalesOrdersCountService) *SalesOrderListener {
	return &SalesOrderListener{
		salesOrdersCountService: salesOrdersCountService,
	}
}

func (s *SalesOrderListener) Name() string {
	return constant.AsyncTaskGroupSalesOrder
}

func (s *SalesOrderListener) MsgHandle(ctx context.Context, message *entity.SaturnMessage) *entity.SaturnReply {
	var (
		msg = &shared_service_common_message.MsgBusMessage{}
	)

	if err := proto.Unmarshal(message.MsgText, msg); err != nil {
		_ = monitor.ReportEvent(constant.SalesOrderVolumeControl, constant.AsyncTaskGroupSalesOrder, constant.MetricMarshalOrderPbError, fmt.Sprintf("Unmarshal message bus err=%v", err))
		Logger.CtxLogErrorf(ctx, "Unmarshal message bus err=%v", err)
		return api.WriteSaturnReply(ctx, fsserr.New(fsserr.DataErr, "unmarshal message bus error"))
	}
	if msg.GetPayload() == nil {
		_ = monitor.ReportEvent(constant.SalesOrderVolumeControl, constant.AsyncTaskGroupSalesOrder, constant.MetricOrderMsgPayloadIsNil, "message payload is nil")
		Logger.CtxLogErrorf(ctx, "msg payload is nil")
		return api.WriteSaturnReply(ctx, fsserr.New(fsserr.DataErr, "message payload is nil"))
	}

	var (
		newData = &order_message_bus.Order{}
		oldData = &order_message_bus.Order{}
	)

	if err := proto.Unmarshal(msg.GetPayload().GetOldData(), oldData); err != nil {
		_ = monitor.ReportEvent(constant.SalesOrderVolumeControl, constant.AsyncTaskGroupSalesOrder, constant.MetricMarshalOrderPbError, fmt.Sprintf("Unmarshal old data err=%v", err))
		Logger.CtxLogErrorf(ctx, "Unmarshal old data err=%v", err)
		return api.WriteSaturnReply(ctx, fsserr.New(fsserr.DataErr, "unmarshal old data err"))
	}
	if err := proto.Unmarshal(msg.GetPayload().GetNewData(), newData); err != nil {
		_ = monitor.ReportEvent(constant.SalesOrderVolumeControl, constant.AsyncTaskGroupSalesOrder, constant.MetricMarshalOrderPbError, fmt.Sprintf("Unmarshal new data err=%v", err))
		Logger.CtxLogErrorf(ctx, "Unmarshal new data err=%v", err)
		return api.WriteSaturnReply(ctx, fsserr.New(fsserr.DataErr, "unmarshal new data err"))
	}
	extInfo := &order_message_bus.OrderExtInfo{}
	if err := proto.Unmarshal(newData.GetExtinfo(), extInfo); err != nil {
		_ = monitor.ReportEvent(constant.SalesOrderVolumeControl, constant.AsyncTaskGroupSalesOrder, constant.MetricMarshalOrderPbError, fmt.Sprintf("Unmarshal order ext info err=%v", err))
		Logger.CtxLogErrorf(ctx, "Unmarshal order ext info err=%v", err)
		return api.WriteSaturnReply(ctx, fsserr.New(fsserr.DataErr, "unmarshal order ext info err"))
	}

	if err := s.salesOrdersCountService.CountShopSalesOrder(ctx,
		uint64(newData.GetShopid()),
		uint64(newData.GetOrderid()),
		int(oldData.GetStatus()),
		int(newData.GetStatus()),
		extInfo.GetDetailFlag(),
		int64(newData.GetCreateTime()),
	); err != nil {
		_ = monitor.ReportEvent(constant.SalesOrderVolumeControl, constant.AsyncTaskGroupSalesOrder, constant.MetricCountShopSalesOrderError, fmt.Sprintf("Count shop sales order err=%v", err))
		Logger.CtxLogErrorf(ctx, "Count shop sales order err=%v", err)
		return api.WriteSaturnReply(ctx, fsserr.New(fsserr.DataErr, "count shop sales order err"))
	}
	return api.WriteSaturnReply(ctx, nil)
}
