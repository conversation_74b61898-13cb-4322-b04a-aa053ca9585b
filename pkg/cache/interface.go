package cache

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type (
	Options[Option any] func(o *Option)
)

type Cache[V any, Option any] interface {
	Get(ctx context.Context, key string, opts ...Options[Option]) (V, bool)
	Set(ctx context.Context, key string, val V, opts ...Options[Option]) bool
}

type MultiCache[V any, Option any] interface {
	Cache[V, Option]
	// MGet returns a slice of Optional[V] to indicate whether the value is found or not
	MGet(ctx context.Context, keys []string, opts ...Options[Option]) ([]typ.Optional[V], int)
	MSet(ctx context.Context, kvMap map[string]V, opts ...Options[Option]) int
}

type LoaderCache[V any, Option any] interface {
	MultiCache[V, Option]
	Load(ctx context.Context, key string, opts ...Options[Option]) (V, error)
}

type MultiLoaderCache[V any, Option any] interface {
	MultiCache[V, Option]
	LoadMany(ctx context.Context, keys []string, opts ...Options[Option]) (map[string]V, error)
}
