package layer_cache

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/modern-go/reflect2"
	"github.com/zeromicro/go-zero/core/syncx"

	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/api"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
)

var (
	errDataLoaderMiss        = errors.New("data loader not found")
	errDataNotFound          = errors.New("data not found")
	errNamespaceNotFound     = errors.New("namespace not found")
	errUnmarshalFuncNotFound = errors.New("unmarshal func not found")
	errDataLoadValueNotFound = errors.New("data load value not found")
)

type LayerCache interface {
	Namespace() Namespace
	Get(ctx context.Context, id string, opts ...LevelOption) (interface{}, error)
	Set(ctx context.Context, id string, obj interface{}, opts ...LevelOption) error
}

func NewLayerCache(namespace Namespace, cache api.CacheClient, confAccessor config.ConfAccessor) LayerCache {
	return &layerCache{
		namespace:    namespace,
		cache:        cache,
		confAccessor: confAccessor,
		singleFlight: syncx.NewSingleFlight(),
	}
}

type layerCache struct {
	namespace    Namespace
	cache        api.CacheClient
	confAccessor config.ConfAccessor
	singleFlight syncx.SingleFlight
}

func (p *layerCache) Namespace() Namespace {
	return p.namespace
}

func (p *layerCache) Get(ctx context.Context, id string, opts ...LevelOption) (interface{}, error) {
	return p.doGet(ctx, id, opts...)
}

func (p *layerCache) doGet(ctx context.Context, id string, opts ...LevelOption) (interface{}, error) {
	layerCacheConfig := p.confAccessor.GetLayerCacheConfig(ctx)
	// get the default expireSeconds
	cacheOption := LevelOptions{
		expiration: uint32(layerCacheConfig.MemPhysicalTTL),
	}
	timeout := defaultTimeout
	if layerCacheExpireConfig, layerCacheExpireConfigOk := layerCacheConfig.ExpireConfig[string(p.namespace)]; layerCacheExpireConfigOk {
		cacheOption.expiration = layerCacheExpireConfig.ExpireSeconds
		if layerCacheExpireConfig.Timeout > 0 {
			timeout = time.Duration(layerCacheExpireConfig.Timeout) * time.Millisecond
		}
	}
	if cacheOption.expiration > 0 {
		cacheOption.expiration = defaultCacheDuration
	}
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if len(opts) > 0 {
		for _, opt := range opts {
			opt(&cacheOption)
		}
	}

	key := getKey(p.namespace, id, cacheOption.keyWithoutNamespace)
	// get from cache with unmarshal function
	if cacheOption.unmarshalFunc == nil {
		sendMonitor(ctx, p.namespace, id, constant.StatusUnmarshallFuncNotFound)
		return nil, errUnmarshalFuncNotFound
	}
	var data interface{}
	var cacheErr error
	data, cacheErr = p.cache.Get(ctx, key).Interface(func(bytes []byte) (interface{}, error) {
		return cacheOption.unmarshalFunc(bytes)
	})
	if cacheErr == nil {
		sendMonitor(ctx, p.namespace, id, constant.StatusSuccess)
		return data, nil
	}

	if cacheOption.loader == nil {
		sendMonitor(ctx, p.namespace, id, constant.StatusLoaderEmpty)
		return nil, errDataLoaderMiss
	}

	//如果缓存没查到，去数据源查询（还有空值、熔断、限流等情况）
	if cacheErr != cache.Miss {
		//抛出一个错误给上层，和约定写法保持一致
		if cacheErr == cache.HotPenetrationError {
			sendMonitor(ctx, p.namespace, id, constant.StatusHotKey)
		} else {
			sendMonitor(ctx, p.namespace, id, constant.StatusError)
		}
		return nil, cacheErr
	}

	// 使用单飞模式降低并发请求
	var fErr error
	data, fErr = p.singleFlight.Do(unique(p.namespace, id), func() (interface{}, error) {
		singleData, err := cacheOption.loader(ctx, id)
		return singleData, err
	})
	if !reflect2.IsNil(fErr) {
		sendMonitor(ctx, p.namespace+"_loadErr", id+fErr.Error(), constant.StatusSuccess)
		return nil, fErr
	}
	if data == nil {
		sendMonitor(ctx, p.namespace+"_loadNil", id, constant.StatusSuccess)
		return nil, errDataNotFound
	}

	sendMonitor(ctx, p.namespace, id, constant.StatusMiss)
	_ = p.cache.Set(ctx, key, data, cacheOption.expiration)
	return data, nil
}

func (p *layerCache) Set(ctx context.Context, id string, obj interface{}, opts ...LevelOption) error {
	cacheOption := LevelOptions{}
	if len(opts) > 0 {
		for _, opt := range opts {
			opt(&cacheOption)
		}
	}

	// layerCache 层只处理同步写入，异步写入由 Manager 层处理
	// 如果这里还收到 asyncSet=true，说明是直接调用，应该返回错误
	if cacheOption.asyncSet {
		return errors.New("async set should be handled at LayerCacheManager level")
	}

	return p.doSet(ctx, id, obj, cacheOption)
}

func (p *layerCache) doSet(ctx context.Context, id string, obj interface{}, cacheOption LevelOptions) error {
	if cacheOption.expiration == 0 {
		layerCacheConfig := p.confAccessor.GetLayerCacheConfig(ctx)
		cacheOption.expiration = uint32(layerCacheConfig.MemPhysicalTTL)
		if layerCacheExpireConfig, layerCacheExpireConfigOk := layerCacheConfig.ExpireConfig[string(p.namespace)]; layerCacheExpireConfigOk && layerCacheExpireConfig.ExpireSeconds > 0 {
			cacheOption.expiration = layerCacheExpireConfig.ExpireSeconds
		}
		if cacheOption.expiration == 0 {
			cacheOption.expiration = defaultCacheDuration
		}
	}
	key := getKey(p.namespace, id, cacheOption.keyWithoutNamespace)
	return p.cache.Set(ctx, key, obj, cacheOption.expiration)
}

func sendMonitor(ctx context.Context, namespace Namespace, msg, status string) {
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLayeredCache, string(namespace), status, msg)
}

func getKey(namespace Namespace, id string, withoutNamespace bool) string {
	if withoutNamespace {
		return id
	}
	return unique(namespace, id)
}

func unique(namespace Namespace, id string) string {
	var builder strings.Builder
	builder.Grow(len(namespace) + len(id) + 1)
	builder.WriteString(string(namespace))
	builder.WriteString(".")
	builder.WriteString(id)
	return builder.String()
}
