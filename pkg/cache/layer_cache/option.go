package layer_cache

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

const (
	Second               = 1
	Minute               = 60
	hotLimitExpiration   = 1 * Minute  // 热数据，最多2分钟过期
	defaultCacheDuration = 5 * Minute  // 默认缓存过期时间
	warmLimitExpiration  = 5 * Minute  // 较热数据，最多5分钟过期
	coldLimitExpiration  = 30 * Minute // 较冷数据，最多60分钟过期
)

type (
	LevelOptions struct {
		loader        DataLoader
		unmarshalFunc UnmarshalFunc
		expiration    uint32
		asyncSet      bool
		// keyWithoutNamespace 带上后， key 不会待 namespace 前缀
		keyWithoutNamespace bool
	}

	DataLoader    func(ctx context.Context, key string) (interface{}, error)
	UnmarshalFunc func([]byte) (interface{}, error)

	LevelOption = cache.Options[LevelOptions]

	// OptionType 选项类型枚举
	OptionType int
)

const (
	OptionTypeCache OptionType = iota // 缓存相关选项
	OptionTypeAsync                   // 异步相关选项
)

func WithCustomExpire(expSecs uint32) LevelOption {
	return func(o *LevelOptions) {
		o.expiration = expSecs
	}
}

func WithHotExpire() LevelOption {
	return func(o *LevelOptions) {
		o.expiration = hotLimitExpiration
	}
}

func WithWarmExpire() LevelOption {
	return func(o *LevelOptions) {
		o.expiration = warmLimitExpiration
	}
}

func WithColdExpire() LevelOption {
	return func(o *LevelOptions) {
		o.expiration = coldLimitExpiration
	}
}

func WithLoader(loader DataLoader) LevelOption {
	return func(o *LevelOptions) {
		o.loader = loader
	}
}

func WithUnmarshalFunc(unmarshalFunc UnmarshalFunc) LevelOption {
	return func(o *LevelOptions) {
		o.unmarshalFunc = unmarshalFunc
	}
}

func WithAsyncSet() LevelOption {
	return func(o *LevelOptions) {
		o.asyncSet = true
	}
}

func WithKeyWithoutNamespace() LevelOption {
	return func(o *LevelOptions) {
		o.keyWithoutNamespace = true
	}
}

// GetOptionType 获取选项类型
func GetOptionType(opt LevelOption) OptionType {
	testOption := LevelOptions{}
	opt(&testOption)
	if testOption.asyncSet {
		return OptionTypeAsync
	}
	return OptionTypeCache
}

// SeparateOptions 分离选项为缓存选项和异步选项
func SeparateOptions(opts []LevelOption) (cacheOpts []LevelOption, hasAsync bool) {
	if len(opts) == 0 {
		return opts, false
	}

	cacheOpts = make([]LevelOption, 0, len(opts))
	for _, opt := range opts {
		if opt != nil {
			if GetOptionType(opt) == OptionTypeAsync {
				hasAsync = true
			} else {
				cacheOpts = append(cacheOpts, opt)
			}
		}
	}
	return cacheOpts, hasAsync
}
