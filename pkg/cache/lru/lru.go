package lru

import (
	"context"
	"math/rand"
	"time"

	lru "git.garena.com/shopee/bg-logistics/go/lrucache"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/randutil"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/timeutil"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type Cache[V any] interface {
	Name() cache.Name
	Get(ctx context.Context, key string, options ...cache.Options[Option]) (V, bool)
	Set(ctx context.Context, key string, val V, options ...cache.Options[Option]) bool
	MGet(ctx context.Context, keys []string, options ...cache.Options[Option]) (data []typ.Optional[V], missingCount int)
	MSet(ctx context.Context, kvMap map[string]V, options ...cache.Options[Option]) int
	Exist(ctx context.Context, key string) bool
	Len(ctx context.Context) int
}

type Dependency struct {
	ConfAccessor config.ConfAccessor
	init         bool
}

var (
	dependency Dependency
)

func InitLruDependency(ctx context.Context, confAccessor config.ConfAccessor) error {
	dependency.ConfAccessor = confAccessor
	dependency.init = true
	return nil
}

// Value 元素包装值
// 与原生value的区别：增加了过期时间
// 目的：快速淘汰元素，不等lru队列填满，即进行释放，提高使用率
type Value[T any] struct {
	Item       T     // 缓存的元素
	Expiration int64 // 过期时间（时间戳）
}

type LruCache[T any] struct {
	name    cache.Name    // LRU缓存实例名称
	timeout time.Duration // LRU 缓存过期时间
	Client  lru.Cache     // lruCacheClient
	size    int           // 初始化缓存长度
	enable  bool          // LruCache 是否可用
	empty   T
}

const (
	defaultSize    int = 1000             // 默认缓存长度
	defaultTimeout     = 60 * time.Second // 默认过期时长，60s，默认单位：秒（s）
	resizeTimeout      = 300              // 变更时间默认 300s，随机在 300 秒完成 resize
)

// NewLruCache 创建LRU内存缓存实例
// @param lruName 缓存实例名称
// @return LRU缓存实例 *LruCache
// <AUTHOR> Bo | SLS BE | <EMAIL>
func NewLruCache[T any](lruName cache.Name, opts ...InitOptions) (Cache[T], error) {
	// 定义LRU缓存实例
	options := new(InitOption)
	for _, opt := range opts {
		opt(options)
	}

	lc := new(LruCache[T])
	lc.name = lruName
	if options.Size <= 0 {
		options.Size = defaultSize
	}
	if options.Timeout <= 0 {
		options.Timeout = defaultTimeout
	}
	lc.size = options.Size
	lc.timeout = options.Timeout
	// 默认为true
	lc.enable = true
	var empty T
	// 初始化lruCacheClient，采用默认值进行初始化，在增加元素时再进行重新计算size
	l, err := lru.NewLruCache(string(lruName), lc.size, lru.WithModel(empty))
	if err != nil {
		return nil, err
	}
	lc.Client = l

	return lc, nil
}

// expired 过期
// @return true：元素过期；false：元素未过期
// <AUTHOR>
func (lv *Value[T]) expired(ctx context.Context) bool {
	if lv != nil && timeutil.Now(ctx).UnixNano() > lv.Expiration {
		return true
	}
	return false
}

// newLruValue 设置元素值与超时时长
// @param value 缓存元素的value
// @param expire 超时时长
// @return true：元素过期；false：元素未过期
// <AUTHOR>
func newLruValue[T any](ctx context.Context, value T, expire time.Duration) *Value[T] {
	lruValue := new(Value[T])
	var endTime int64
	if expire == 0 {
		expire = defaultTimeout
	}
	if expire > 0 {
		endTime = timeutil.Now(ctx).Add(expire).UnixNano()
	}
	lruValue.Item = value
	lruValue.Expiration = endTime
	return lruValue
}

func (lc *LruCache[T]) doGet(ctx context.Context, key string) (T, bool) {
	if lc != nil && lc.Client != nil {
		if !lc.enable {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotEnable, "")
			return lc.empty, false
		}
		val, ok := lc.Client.Get(ctx, key)
		if !ok {
			if lc.Client.Len(ctx) >= lc.size {
				_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusLru, "")
			} else {
				_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusNotExist, "")
			}
			return lc.empty, false
		}
		lruValue, ok := val.(*Value[T])
		if !ok {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusError, "")
			return lc.empty, false
		}
		if lruValue.expired(ctx) {
			_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusExpired, "")
			lc.Client.Remove(ctx, key)
			return lc.empty, false
		}
		_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, string(lc.name), constant.StatusSuccess, "")
		return lruValue.Item, true
	}

	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLruCache, "cache", constant.StatusError, "cache is null")
	return lc.empty, false
}

func (lc *LruCache[T]) Name() cache.Name {
	return lc.name
}

func (lc *LruCache[T]) Get(ctx context.Context, key string, options ...cache.Options[Option]) (T, bool) {
	return lc.doGet(ctx, key)
}

func (lc *LruCache[T]) MGet(ctx context.Context, keys []string, options ...cache.Options[Option]) ([]typ.Optional[T], int) {
	missingCount := 0
	ret := make([]typ.Optional[T], 0, len(keys))
	for _, key := range keys {
		val, ok := lc.doGet(ctx, key)
		if ok {
			ret = append(ret, typ.NewOptional(val))
		} else {
			ret = append(ret, typ.Empty[T]())
			missingCount++
		}
	}
	return ret, missingCount
}

func (lc *LruCache[T]) Set(ctx context.Context, key string, val T, options ...cache.Options[Option]) bool {
	timeout, size, enable := lc.getParam(ctx)
	if !enable {
		if !lc.enable {
			return false
		} else {
			lc.enable = false
			lc.resize(ctx, 0)
			return false
		}
	}

	if !lc.enable {
		lc.enable = true
	}
	// 增加、减少元素时，重新设置LRU缓存长度，不在get时计算，因为查询远多过新增
	lc.resize(ctx, size)

	opt := Option{}
	for _, o := range options {
		o(&opt)
	}
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}

	if lc != nil && lc.Client != nil {
		lruValue := newLruValue(ctx, val, timeout)
		return lc.Client.Add(ctx, key, lruValue)
	}
	return false
}

func (lc *LruCache[T]) MSet(ctx context.Context, kvMap map[string]T, options ...cache.Options[Option]) int {
	timeout, size, enable := lc.getParam(ctx)
	if !enable {
		if !lc.enable {
			return 0
		} else {
			lc.enable = false
			lc.resize(ctx, 0)
			return 0
		}
	}

	if !lc.enable {
		lc.enable = true
	}
	// 增加、减少元素时，重新设置LRU缓存长度，不在get时计算，因为查询远多过新增
	lc.resize(ctx, size)

	opt := Option{}
	for _, o := range options {
		o(&opt)
	}
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}

	successCount := 0
	if lc != nil && lc.Client != nil {
		for key, val := range kvMap {
			lruValue := newLruValue(ctx, val, timeout)
			if lc.Client.Add(ctx, key, lruValue) {
				successCount++
			}
		}
	}
	return successCount
}

func (lc *LruCache[T]) Exist(ctx context.Context, key string) bool {
	if lc != nil && lc.Client != nil {
		_, ok := lc.Client.Get(ctx, key)
		return ok
	}
	return false
}

// resize 重新设定LRU缓存长度
//
//	1.小于当前长度，增加随机时间进行移除，避免缓存雪崩
//	2.大于当前长度，则刷新长度
//	3.长度为0,清空缓存
//	@param size 重设定的缓存长度
//	<AUTHOR>
func (lc *LruCache[T]) resize(ctx context.Context, size int) {
	if size == 0 {
		// 清零需要增加睡眠时间，避免缓存雪崩
		Logger.CtxLogInfof(ctx, "lru_cache %v resize purge, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		go func(size int) {
			seconds := randutil.RandIntN(ctx, resizeTimeout)
			time.Sleep(time.Duration(seconds) * time.Second)
			// 双重判断，保证缩容、清零多次触发时只执行最后一次
			if lc.size == size {
				lc.Client.Purge(ctx)
			}
			Logger.CtxLogInfof(ctx, "lru_cache %v resize after %d seconds finish purge", lc.name, seconds)
		}(size)
	} else if lc.size == size {

	} else if lc.size < size {
		// 扩容直接扩
		Logger.CtxLogInfof(ctx, "lru_cache %v resize scale up, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		lc.Client.Resize(ctx, size)
	} else if lc.size > size {
		// 缩容需要增加睡眠时间，避免缓存雪崩
		Logger.CtxLogInfof(ctx, "lru_cache %v resize scale down, source_size:%d, target_size:%d", lc.name, lc.size, size)
		lc.size = size
		go func(size int) {
			rand.Seed(time.Now().UnixNano())
			seconds := rand.Intn(resizeTimeout)
			time.Sleep(time.Duration(seconds) * time.Second)
			// 双重判断，保证缩容、清零多次触发时只执行最后一次
			if lc.size == size {
				lc.Client.Resize(ctx, size)
				Logger.CtxLogInfof(ctx, "lru_cache %v resize after %d seconds finish scale down", lc.name, seconds)
			}
		}(size)
	}
}

// getParam 获取lru参数，来源于Apollo配置
//
//	根据LRU缓存实例名，来查询Apollo对应的配置，若未配置，则取默认值返回
//	@return 过期时长，单位：秒（s） time.Duration
//	@return 缓存长度 int
//	<AUTHOR> Bo | SLS BE | <EMAIL>
func (lc *LruCache[T]) getParam(ctx context.Context) (timeout time.Duration, size int, enable bool) {
	name := string(lc.name)
	timeout = lc.timeout
	size = lc.size
	enable = true

	if dependency.init {
		lruCacheConfig := dependency.ConfAccessor.GetLruCacheConfig(ctx)
		param, ok := lruCacheConfig.Param[name]
		if ok {
			// 配置过期时长数值，默认单位：秒（s），转换为过期时长（time.Duration实例）
			timeout = time.Duration(param.Timeout) * time.Second
			size = param.Size
			enable = param.Enable
		}
	}
	if size <= 0 {
		size = defaultSize
	}
	return timeout, size, enable
}

func (lc *LruCache[T]) Len(ctx context.Context) int {
	return lc.Client.Len(ctx)
}
