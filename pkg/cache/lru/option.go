package lru

import (
	"time"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type InitOption struct {
	Size    int
	Timeout time.Duration
}

type InitOptions func(options *InitOption)

func WithLruSize(size int) InitOptions {
	return func(options *InitOption) {
		if options == nil {
			options = new(InitOption)
		}
		options.Size = size
	}
}

func WithLruTimeout(timeout time.Duration) InitOptions {
	return func(options *InitOption) {
		if options == nil {
			options = new(InitOption)
		}
		options.Timeout = timeout
	}
}

type Option struct {
	Timeout time.Duration
}

func WithTimeout(timeout time.Duration) cache.Options[Option] {
	return func(options *Option) {
		if options == nil {
			options = new(Option)
		}
		options.Timeout = timeout
	}
}
