package multi_layer_cache

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type MultiLayerCache[V any, LocalOption any, RemoteOption any] interface {
	Name() cache.Name
	Get(ctx context.Context, key string, opts ...cache.Options[Option[LocalOption, RemoteOption]]) (V, bool)
	Set(ctx context.Context, key string, val V, opts ...cache.Options[Option[LocalOption, RemoteOption]]) bool
	MGet(ctx context.Context, keys []string, opts ...cache.Options[Option[LocalOption, RemoteOption]]) ([]typ.Optional[V], int)
	MSet(ctx context.Context, kvMap map[string]V, opts ...cache.Options[Option[LocalOption, RemoteOption]]) int
}

type multiLayerCache[V any, LocalOption any, RemoteOption any] struct {
	name   cache.Name
	local  cache.MultiCache[V, LocalOption]
	remote cache.MultiCache[V, RemoteOption]
}

func NewMultiLayerCache[V any, LocalOption any, RemoteOption any](
	name cache.Name,
	local cache.MultiCache[V, LocalOption],
	remote cache.MultiCache[V, RemoteOption],
) MultiLayerCache[V, LocalOption, RemoteOption] {
	return &multiLayerCache[V, LocalOption, RemoteOption]{
		name:   name,
		local:  local,
		remote: remote,
	}
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) Name() cache.Name {
	return m.name
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) Get(ctx context.Context, key string, opts ...cache.Options[Option[LocalOption, RemoteOption]]) (V, bool) {
	opt := getOption(opts...)
	localOptions := toLocalOption(opt)
	ret, ok := m.getLocal(ctx, key, localOptions)
	if ok {
		return ret, true
	}
	remoteOptions := toRemoteOption(opt)
	ret, ok = m.getRemote(ctx, key, remoteOptions)
	if ok {
		m.setLocal(ctx, key, ret, localOptions)
	}
	return ret, ok
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) Set(ctx context.Context, key string, val V, opts ...cache.Options[Option[LocalOption, RemoteOption]]) bool {
	opt := getOption(opts...)
	localOptions := toLocalOption(opt)
	remoteOptions := toRemoteOption(opt)

	ok := m.local.Set(ctx, key, val, localOptions)
	if rok := m.remote.Set(ctx, key, val, remoteOptions); !rok {
		return false
	}
	return ok
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) MGet(ctx context.Context, keys []string, opts ...cache.Options[Option[LocalOption, RemoteOption]]) ([]typ.Optional[V], int) {
	opt := getOption(opts...)
	localOption := toLocalOption(opt)
	remoteOption := toRemoteOption(opt)

	ret, missingCount := m.local.MGet(ctx, keys, localOption)
	if missingCount == 0 {
		return ret, 0
	}

	missingKeys := make([]string, 0, missingCount)
	missKeyIndex := make(map[int]int, missingCount)
	for index, item := range ret {
		if item.Exists() {
			continue
		}
		key := keys[index]
		missingKeys = append(missingKeys, key)
		missKeyIndex[len(missingKeys)-1] = index
	}

	remoteRet, missingCount := m.mGetRemote(ctx, missingKeys, remoteOption)
	findKeys := make(map[string]V, len(missingKeys)-missingCount)
	for index := range remoteRet {
		if !remoteRet[index].Exists() {
			continue
		}
		missIndex := missKeyIndex[index]
		ret[missIndex] = remoteRet[index]
		findKeys[keys[missIndex]] = remoteRet[index].Get()
	}
	if len(findKeys) > 0 {
		m.mSetLocal(ctx, findKeys, localOption)
	}
	return ret, missingCount
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) MSet(ctx context.Context, kvMap map[string]V, opts ...cache.Options[Option[LocalOption, RemoteOption]]) int {
	opt := getOption(opts...)
	localOption := toLocalOption(opt)
	remoteOption := toRemoteOption(opt)
	successCount := m.mSetLocal(ctx, kvMap, localOption)

	_ = m.mSetRemote(ctx, kvMap, remoteOption)
	return successCount
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) getLocal(ctx context.Context, key string, localOpts ...cache.Options[LocalOption]) (V, bool) {
	return m.local.Get(ctx, key, localOpts...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) getRemote(ctx context.Context, key string, remoteOptions ...cache.Options[RemoteOption]) (V, bool) {
	return m.remote.Get(ctx, key, remoteOptions...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) setLocal(ctx context.Context, key string, val V, localOpts ...cache.Options[LocalOption]) bool {
	return m.local.Set(ctx, key, val, localOpts...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) setRemote(ctx context.Context, key string, val V, remoteOptions ...cache.Options[RemoteOption]) bool {
	return m.remote.Set(ctx, key, val, remoteOptions...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) mGetLocal(ctx context.Context, keys []string, localOpts ...cache.Options[LocalOption]) (ret []typ.Optional[V], missingCount int) {
	return m.local.MGet(ctx, keys, localOpts...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) mGetRemote(ctx context.Context, keys []string, remoteOptions ...cache.Options[RemoteOption]) (ret []typ.Optional[V], missingCount int) {
	return m.remote.MGet(ctx, keys, remoteOptions...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) mSetLocal(ctx context.Context, kvMap map[string]V, localOpts ...cache.Options[LocalOption]) int {
	return m.local.MSet(ctx, kvMap, localOpts...)
}

func (m *multiLayerCache[V, LocalOption, RemoteOption]) mSetRemote(ctx context.Context, kvMap map[string]V, remoteOptions ...cache.Options[RemoteOption]) int {
	return m.remote.MSet(ctx, kvMap)
}

func getOption[LocalOption any, RemoteOption any](opts ...cache.Options[Option[LocalOption, RemoteOption]]) Option[LocalOption, RemoteOption] {
	opt := Option[LocalOption, RemoteOption]{}
	for _, o := range opts {
		o(&opt)
	}
	return opt
}
