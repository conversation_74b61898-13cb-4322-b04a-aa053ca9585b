package mixed_cache

import (
	"git.garena.com/shopee/bg-logistics/go/go-redis"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/lru"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/multi_layer_cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/remote_cache"
)

type LruLayerCache[V any] interface {
	multi_layer_cache.MultiLayerCache[V, lru.Option, remote_cache.Option]
}

func NewLruLayerCacheWithRedis[V any](
	clients redishelper.GlobalRedisClients,
	name cache.Name,
	clusterName redishelper.CacheCloudClusterName,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (LruLayerCache[V], error) {
	client, err := clients.GetRedisClusterByClusterName(clusterName)
	if err != nil {
		return nil, err
	}
	return NewLruLayerCache[V](name, client, lruOpts, remoteOpts)
}

func NewLruLayerCache[V any](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (LruLayerCache[V], error) {
	remoteCache := remote_cache.NewRemoteCache[V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[V, lru.Option, remote_cache.Option](name, lruCache, remoteCache), nil
}

func NewBasicLruLayerCache[V typ.Number | ~bool](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (LruLayerCache[V], error) {
	remoteCache := remote_cache.NewBasicRemoteCache[V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[V, lru.Option, remote_cache.Option](name, lruCache, remoteCache), nil

}

func NewStrLruLayerCache[V typ.String](
	name cache.Name,
	client *redis.Client,
	lruOpts []lru.InitOptions,
	remoteOpts []remote_cache.InitOptions[V],
) (multi_layer_cache.MultiLayerCache[V, lru.Option, remote_cache.Option], error) {
	remoteCache := remote_cache.NewStrRemoteCache[V](name, client, remoteOpts...)
	lruCache, err := lru.NewLruCache[V](name, lruOpts...)
	if err != nil {
		return nil, err
	}
	return multi_layer_cache.NewMultiLayerCache[V, lru.Option, remote_cache.Option](name, lruCache, remoteCache), nil
}
