package multi_layer_cache

import (
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type (
	Option[LocalOption any, RemoteOption any] struct {
		Local  LocalOption
		Remote RemoteOption
	}
)

func WithLocalOptions[LocalOption any, RemoteOption any](localOptions cache.Options[LocalOption]) cache.Options[Option[LocalOption, RemoteOption]] {
	return func(options *Option[LocalOption, RemoteOption]) {
		if options == nil {
			options = new(Option[LocalOption, RemoteOption])
		}
		localOptions(&options.Local)
	}
}

func WithRemoteOptions[LocalOption any, RemoteOption any](remoteOptions cache.Options[RemoteOption]) cache.Options[Option[LocalOption, RemoteOption]] {
	return func(options *Option[LocalOption, RemoteOption]) {
		if options == nil {
			options = new(Option[LocalOption, RemoteOption])
		}
		remoteOptions(&options.Remote)
	}
}

func toLocalOption[LocalOption any, RemoteOption any](opt Option[LocalOption, RemoteOption]) cache.Options[LocalOption] {
	return func(o *LocalOption) {
		if o == nil {
			return
		}
		*o = opt.Local
	}
}

func toRemoteOption[LocalOption any, RemoteOption any](opt Option[LocalOption, RemoteOption]) cache.Options[RemoteOption] {
	return func(o *RemoteOption) {
		if o == nil {
			return
		}
		*o = opt.Remote
	}
}
