package cache

import (
	"context"
)

func MultiGetMany[K comparable, V any, Option any](
	ctx context.Context,
	cache MultiCache[V, Option],
	rawKeys []K,
	keyFunc keyConvertor[K],
	opts ...Options[Option],
) map[K]V {
	cacheKeys := make([]string, 0, len(rawKeys))
	for _, key := range rawKeys {
		cacheKey := keyFunc(key)
		cacheKeys = append(cacheKeys, cacheKey)
	}
	cacheResult, missedCount := cache.MGet(ctx, cacheKeys, opts...)
	ret := make(map[K]V, len(rawKeys)-missedCount)
	for index := range cacheResult {
		cacheValue := cacheResult[index]
		key := rawKeys[index]
		if !cacheValue.Exists() {
			continue
		}
		ret[key] = cacheValue.Get()
	}
	return ret
}

// MultiLoadManyFromMap 通用缓存加载函数，实现缓存旁路模式
// 现在直接使用LRU缓存的LoadManyWithKeyFunc函数
// K: 键类型 (如 uint64, string 等)
// V: 值类型 (如 entity.ShopDisplayChannelData 等)
//
// 参数说明:
// - ctx: 上下文
// - cache: LRU缓存实例
// - keys: 需要查询的键列表
// - keyFunc: 将键转换为缓存key的函数
// - fetchFunc: 当缓存未命中时，从数据源获取数据的函数
func MultiLoadManyFromMap[K comparable, V any, Option any](
	ctx context.Context,
	cache MultiCache[V, Option],
	keys []K,
	keyFunc keyConvertor[K],
	fetchFunc MapFetchFunc[K, V],
	opts ...Options[Option],
) (map[K]V, error) {
	var result = make(map[K]V)

	var cacheKeys []string = make([]string, 0, len(keys))
	for _, key := range keys {
		cacheKey := keyFunc(key)
		cacheKeys = append(cacheKeys, cacheKey)
	}

	// 第一步：尝试从缓存获取
	cacheResult, missedCount := cache.MGet(ctx, cacheKeys, opts...)
	var missedKeys []K = make([]K, 0, missedCount)
	for index := range cacheResult {
		cacheValue := cacheResult[index]
		key := keys[index]
		if !cacheValue.Exists() {
			missedKeys = append(missedKeys, key)
			continue
		}
		result[key] = cacheValue.Get()
	}

	// 第二步：如果有缓存未命中的键，从数据源获取
	if len(missedKeys) > 0 {
		fetchedData, err := fetchFunc(ctx, missedKeys)
		if err != nil {
			return nil, err
		}

		cacheMap := make(map[string]V, len(fetchedData))
		// 第三步：将获取的数据加入缓存并添加到结果中
		for key, data := range fetchedData {
			cacheMap[keyFunc(key)] = data
			result[key] = data
		}
		cache.MSet(ctx, cacheMap, opts...)
	}
	return result, nil
}

// MultiLoadManyFromSlice 通用缓存加载函数，适用于返回切片的场景
// K: 键类型
// V: 值类型
//
// 参数说明:
// - ctx: 上下文
// - cache: LRU缓存实例
// - keys: 需要查询的键列表
// - keyFunc: 将键转换为缓存key的函数
// - fetchFunc: 当缓存未命中时，从数据源获取数据的函数（返回切片）
// - extractKeyFunc: 从获取的数据中提取键的函数
func MultiLoadManyFromSlice[K comparable, V any, Option any](
	ctx context.Context,
	cache MultiCache[V, Option],
	keys []K,
	keyFunc keyConvertor[K],
	fetchFunc SliceFetchFunc[K, V],
	extractKeyFunc func(V) K,
	opts ...Options[Option],
) (map[K]V, error) {
	// 包装fetchFunc，将切片转换为map
	wrappedFetchFunc := func(ctx context.Context, missedKeys []K) (map[K]V, error) {
		slice, err := fetchFunc(ctx, missedKeys)
		if err != nil {
			return nil, err
		}

		result := make(map[K]V)
		for _, item := range slice {
			key := extractKeyFunc(item)
			result[key] = item
		}
		return result, nil
	}

	return MultiLoadManyFromMap(ctx, cache, keys, keyFunc, wrappedFetchFunc, opts...)
}
