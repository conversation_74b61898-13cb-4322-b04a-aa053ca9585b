package cache

type Name string

// LruCacheName LRU缓存实例名称
const (
	EntityTagLruName   Name = "EntityTagApi"
	TagValueLruName    Name = "TagValueApi"
	ItemInfoLruName    Name = "ItemInfoApi"
	ShopChannelLruName Name = "ShopChannelApi"
	ChannelLruName     Name = "ChannelApi"
)

// LocalCacheName 本地缓存实例名称
const (
	Channels          Name = "Channels"
	WarehouseChannels Name = "WarehouseChannels"
	ApiTokens         Name = "ApiTokens"
)

// LayerCacheName 分层缓存实例名称
const ()

// MultiLayerCacheName 多层缓存实例名称
const (
	SocVersionCacheName          Name = "SoCVersion"
	SoCServiceabilityCacheName   Name = "SOCServiceability"
	WarehousePriorityCacheName   Name = "WarehousePriority"
	ShopLocalSipCacheName        Name = "ShopLocalSIP"
	ShopWhPriorityCacheName      Name = "ShopWarehousePriority"
	ShopWhitelistDetailCacheName Name = "ShopWhitelistDetailCache"
	ShopWarehouseListCacheName   Name = "ShopWarehouseList"
	ShopWarehouseGeoCacheName    Name = "ShopWarehouseGeo"
	ShopSellerUserIdCacheName    Name = "ShopSellerUserId"
	ShopMultiWHFlagCacheName     Name = "ShopMultiWHFlag"
)
