package remote_cache

import (
	"context"
	"strings"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type RemoteCache[V any] interface {
	Name() cache.Name
	Get(ctx context.Context, key string, opts ...cache.Options[Option]) (V, bool)
	Set(ctx context.Context, key string, val V, opts ...cache.Options[Option]) bool
	MGet(ctx context.Context, keys []string, opts ...cache.Options[Option]) ([]typ.Optional[V], int)
	MSet(ctx context.Context, kvMap map[string]V, opts ...cache.Options[Option]) int
}

func NewBasicRemoteCache[V typ.Number | ~bool](
	name cache.Name,
	remoteCache *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[V] {
	opts = append(opts, WithMarshalFunc[V](BasicMarshal[V]))
	return NewRemoteCache(name, remoteCache, opts...)
}

func NewStrRemoteCache[V typ.String](
	name cache.Name,
	remoteCache *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[V] {
	opts = append(opts, WithUnmarshalFunc[V](StringUnmarshal[V]), WithMarshalFunc[V](BasicMarshal[V]))
	return NewRemoteCache(name, remoteCache, opts...)
}

func NewRemoteCache[V any](
	name cache.Name,
	client *redis.Client,
	opts ...InitOptions[V],
) RemoteCache[V] {
	// 默认使用 json
	opt := InitOption[V]{
		UnmarshalFunc: JsonUnmarshal[V],
		MarshalFunc:   JsonMarshal[V],
	}
	for _, o := range opts {
		o(&opt)
	}

	return &remoteCache[V]{
		name:             name,
		client:           client,
		marshal:          opt.MarshalFunc,
		unmarshal:        opt.UnmarshalFunc,
		withoutNamespace: opt.WithoutNamespace,
	}
}

type remoteCache[V any] struct {
	name             cache.Name
	client           *redis.Client
	marshal          MarshalFunc[V]
	unmarshal        UnmarshalFunc[V]
	withoutNamespace bool
}

func (r *remoteCache[V]) Name() cache.Name {
	return r.name
}

func (r *remoteCache[V]) Get(ctx context.Context, key string, opts ...cache.Options[Option]) (V, bool) {
	value, gErr := r.client.Get(ctx, r.remoteKey(key)).Result()
	if gErr != nil {
		var empty V
		return empty, false
	}
	val, err := r.unmarshal(ctx, value)
	if err != nil {
		var empty V
		return empty, false
	}
	return val, true
}

func (r *remoteCache[V]) Set(ctx context.Context, key string, val V, opts ...cache.Options[Option]) bool {
	valStr, err := r.marshal(ctx, val)
	if err != nil {
		return false
	}

	opt := getOption(opts...)
	var timeout time.Duration
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}
	err = r.client.Set(ctx, r.remoteKey(key), valStr, timeout).Err()
	return err == nil
}

func (r *remoteCache[V]) MGet(ctx context.Context, keys []string, opts ...cache.Options[Option]) ([]typ.Optional[V], int) {
	remoteKeys := make([]string, len(keys))
	for index, key := range keys {
		remoteKeys[index] = r.remoteKey(key)
	}
	values, gErr := r.client.MGet(ctx, remoteKeys...).Result()
	if gErr != nil {
		return nil, len(remoteKeys)
	}

	missingCount := 0
	ret := make([]typ.Optional[V], len(keys))
	for i := range values {
		valueI := values[i]
		if valueI == nil {
			missingCount++
			continue
		}
		value, ok := valueI.(string)
		if !ok {
			missingCount++
			continue
		}
		val, err := r.unmarshal(ctx, value)
		if err != nil {
			ret[i] = typ.Empty[V]()
			missingCount++
			continue
		}
		ret[i] = typ.NewOptional(val)
	}
	return ret, missingCount
}

func (r *remoteCache[V]) MSet(ctx context.Context, kvMap map[string]V, opts ...cache.Options[Option]) int {
	opt := getOption(opts...)
	var timeout time.Duration
	if opt.Timeout > 0 {
		timeout = opt.Timeout
	}
	if timeout > 0 {
		return r.mSetWithTimeout(ctx, kvMap, timeout)
	}
	return r.mSet(ctx, kvMap)
}

func (r *remoteCache[V]) mSet(ctx context.Context, kvMap map[string]V) int {
	values := make([]interface{}, 0, len(kvMap)*2)
	successCount := 0
	for key, val := range kvMap {
		valStr, err := r.marshal(ctx, val)
		if err != nil {
			continue
		}
		successCount++
		values = append(values, r.remoteKey(key))
		values = append(values, valStr)
	}
	if err := r.client.MSet(ctx, values...).Err(); err != nil {
		return 0
	}
	return successCount
}

func (r *remoteCache[V]) mSetWithTimeout(ctx context.Context, kvMap map[string]V, timeout time.Duration) int {
	successCount := 0
	pipeline := r.client.Pipeline()
	for key, value := range kvMap {
		valStr, err := r.marshal(ctx, value)
		if err != nil {
			continue
		}
		successCount++
		pipeline.Set(ctx, r.remoteKey(key), valStr, timeout)
	}
	_, execErr := pipeline.Exec(ctx)
	if execErr != nil {
		return 0
	}
	return successCount
}

func (r *remoteCache[V]) remoteKey(key string) string {
	if r.withoutNamespace {
		return key
	}
	return strings.Join([]string{string(r.name), key}, ".")
}

func getOption(opts ...cache.Options[Option]) Option {
	opt := Option{}
	for _, o := range opts {
		o(&opt)
	}
	return opt
}
