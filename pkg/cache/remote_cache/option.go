package remote_cache

import (
	"context"
	"time"

	"github.com/bytedance/sonic"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
)

type (
	UnmarshalFunc[V any] func(ctx context.Context, data string) (V, error)
	MarshalFunc[V any]   func(ctx context.Context, data V) (interface{}, error)
)

func JsonUnmarshal[V any](ctx context.Context, data string) (V, error) {
	var v V
	if err := sonic.UnmarshalString(data, &v); err != nil {
		return v, err
	}
	return v, nil
}

func StringUnmarshal[V ~string](ctx context.Context, data string) (V, error) {
	return V(data), nil
}

func JsonMarshal[V any](ctx context.Context, data V) (interface{}, error) {
	return sonic.MarshalString(data)
}

// BasicMarshal 用于基本类型，直接返回原始值, redis 会自动序列化为字符串
func BasicMarshal[V typ.Number | typ.String | ~bool](ctx context.Context, data V) (interface{}, error) {
	return data, nil
}

type (
	InitOption[V any] struct {
		WithoutNamespace bool
		MarshalFunc      MarshalFunc[V]
		UnmarshalFunc    UnmarshalFunc[V]
	}

	InitOptions[V any] func(options *InitOption[V])
)

func WithoutNamespace[V any]() InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		options.WithoutNamespace = true
	}
}

func WithMarshalFunc[V any](marshalFunc MarshalFunc[V]) InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		if marshalFunc == nil {
			return
		}
		options.MarshalFunc = marshalFunc
	}
}

func WithUnmarshalFunc[V any](unmarshalFunc UnmarshalFunc[V]) InitOptions[V] {
	return func(options *InitOption[V]) {
		if options == nil {
			options = new(InitOption[V])
		}
		if unmarshalFunc == nil {
			return
		}
		options.UnmarshalFunc = unmarshalFunc
	}
}

type (
	Option struct {
		Timeout time.Duration
	}

	Options = cache.Options[Option]
)

func WithTimeout(timeout time.Duration) Options {
	return func(options *Option) {
		if options == nil {
			options = new(Option)
		}
		options.Timeout = timeout
	}
}
