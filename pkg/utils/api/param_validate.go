package api

import (
	"context"
	"fmt"
	"io"
	"mime"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"gopkg.in/go-playground/validator.v8"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/api_token"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/cache/localcache"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

const (
	MIMEJSON              = "application/json"
	MIMEPOSTForm          = "application/x-www-form-urlencoded"
	MIMEMultipartPOSTForm = "multipart/form-data"
)

// 全局变量
var validate = validator.New(&validator.Config{TagName: "validate"})

// arrangeValidateError 格式化验证错误信息（测试用）
func arrangeValidateError(err error) error {
	validateErrs, ok := err.(validator.ValidationErrors)
	if !ok {
		return err
	}
	var fieldErrTips []string
	for _, fieldErr := range validateErrs {
		fieldName := fieldErr.Field
		if fieldName == "" {
			fieldName = fieldErr.Name
		}
		fieldErrTips = append(fieldErrTips, fmt.Sprintf("param:<%s> is limited [%s:%+v], but actual value is %+v",
			fieldName,
			fieldErr.Tag,
			fieldErr.Param,
			fieldErr.Value))
	}
	return fmt.Errorf(strings.Join(fieldErrTips, ";"))
}

// ProtoSchemaParseValidate 统一的参数解析和验证函数
// 支持GET请求的URL查询参数、POST请求的form数据、JSON数据和protobuf数据解析
// 参数:
//   - ctx: restful上下文
//   - schemaPtr: 指向结构体的指针，用于接收解析后的参数
//
// 返回: fsserr.Error 类型的错误信息
func ProtoSchemaParseValidate(ctx *restful.Context, schemaPtr PBRequest) fsserr.Error {
	tokenParam := ctx.ReadHeader("Token")
	localCache, err := localcache.GetCache[string, *api_token.ApiTokenTab](cache.ApiTokens)
	if err != nil {
		return fsserr.With(fsserr.LocalCacheErr, err)
	}
	resultsMap := localCache.MGet(ctx.Ctx, []string{tokenParam})
	if _, exist := resultsMap[tokenParam]; !exist {
		return fsserr.With(fsserr.ParamErr, errors.New("invalid token"))
	}

	// 数据读取
	if err := ProtoSchemaParse(ctx, schemaPtr); err != nil {
		return err
	}

	// 如果是 protobuf 请求，存储到 context 中用于日志记录
	if IsProtobufRequest(ctx) {
		// 从请求路径推断操作名称
		operation := extractOperationFromPath(ctx.Request().Request.URL.Path)
		ctx.Ctx = ctxutils.SetPBRequest(ctx.Ctx, schemaPtr, operation)
	}

	// 数据校验
	return SchemaValidate(schemaPtr)
}

func JsonSchemaParseValidate(ctx *restful.Context, schemaPtr interface{}) fsserr.Error {
	// 数据读取
	if err := JsonSchemaParse(ctx, schemaPtr); err != nil {
		return err
	}
	if err := validate.Struct(schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, arrangeValidateError(err))
	}
	if s, ok := schemaPtr.(Request); ok {
		return SchemaValidate(s)
	}
	return nil
}

// extractOperationFromPath 从请求路径提取操作名称
func extractOperationFromPath(path string) string {
	parts := strings.Split(path, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return "unknown"
}

// ProtoSchemaParse 数据读取抽象函数
// 根据请求类型读取数据到schemaPtr中
func ProtoSchemaParse(ctx *restful.Context, schemaPtr PBRequest) fsserr.Error {
	contentType := ctx.Request().HeaderParameter("Content-Type")
	switch {
	case strings.Contains(contentType, "application/x-protobuf"):
		return parseProtobufRequest(ctx, schemaPtr)
	case strings.Contains(contentType, "application/json"):
		return parseJSONPBData(ctx, schemaPtr)
	default:
		return JsonSchemaParse(ctx, schemaPtr)
	}
}

func JsonSchemaParse(ctx *restful.Context, schemaPtr interface{}) fsserr.Error {
	contentType := ctx.Request().HeaderParameter("Content-Type")
	method := ctx.Request().Request.Method
	switch {
	case strings.Contains(contentType, "application/json"):
		return parseJSONData(ctx, schemaPtr)
	case method == http.MethodGet:
		return parseURLQuery(ctx, schemaPtr)
	case strings.Contains(contentType, "form"):
		return parseFormData(ctx, schemaPtr)
	default:
		return parseJSONData(ctx, schemaPtr)
	}
}

// SchemaValidate 数据校验抽象函数
func SchemaValidate(schemaPtr Request) fsserr.Error {
	if err := schemaPtr.Validate(); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	return nil
}

// parseProtobufRequest 解析protobuf请求
func parseProtobufRequest(ctx *restful.Context, schemaPtr PBRequest) fsserr.Error {
	// 读取请求体
	body, err := io.ReadAll(ctx.Request().Request.Body)
	if err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}

	// 反序列化protobuf消息
	if err := proto.Unmarshal(body, schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}

	return nil
}

// parseJSONPBData proto 方式解析JSON数据
func parseJSONPBData(ctx *restful.Context, schemaPtr PBRequest) fsserr.Error {
	// 读取请求体
	body, err := io.ReadAll(ctx.Request().Request.Body)
	if err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	if err = unmarshaler.Unmarshal(body, schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	return nil
}

// parseURLQuery 解析URL查询参数
func parseURLQuery(ctx *restful.Context, schemaPtr interface{}) fsserr.Error {
	if err := readURLQuery(ctx.Ctx, ctx.Request().Request.URL.Query(), schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	return nil
}

// parseFormData 解析form数据
func parseFormData(ctx *restful.Context, schemaPtr interface{}) fsserr.Error {
	if err := ctx.ReadQueryEntity(schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	//1）解析post请求body中form格式的参数，并且不会覆盖ReadQueryEntity（即在url中的参数
	//2）只有ReadQueryEntity读取不到的时候才会在此处赋值
	if err := readFormOfPostBody(ctx, schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	return nil
}

// parseJSONData 方式解析JSON数据
func parseJSONData(ctx *restful.Context, schemaPtr interface{}) fsserr.Error {
	// 读取请求体
	if err := ctx.ReadEntity(schemaPtr); err != nil {
		return fsserr.With(fsserr.ParamErr, err)
	}
	return nil
}

var (
	marshaler = protojson.MarshalOptions{
		AllowPartial:   true,
		UseProtoNames:  true,
		UseEnumNumbers: true,
	}
)

var (
	unmarshaler = protojson.UnmarshalOptions{
		AllowPartial:   true,
		DiscardUnknown: true,
	}
)

// readURLQuery 解析URL查询参数到结构体
func readURLQuery(ctx context.Context, queries map[string][]string, schemaPtr interface{}) error {
	eType := reflect.TypeOf(schemaPtr).Elem()
	eVal := reflect.ValueOf(schemaPtr).Elem()
	for i := 0; i < eVal.NumField(); i++ {
		fVal := eVal.Field(i)
		if !fVal.CanSet() {
			continue
		}
		if fVal.Kind() == reflect.Struct {
			for j := 0; j < fVal.NumField(); j++ {
				gVal := fVal.Field(j)
				if !gVal.CanSet() {
					continue
				}
				gType := fVal.Type()
				tag := gType.Field(j).Tag.Get("json")
				input, ok := queries[tag]
				if !ok {
					continue
				}
				if err := convert(input, gVal); err != nil {
					logger.CtxLogErrorf(ctx, "parse url query error, tag:%s, error:%+v", tag, err)
					return err
				}
			}
		} else {
			tagSlice := strings.Split(eType.Field(i).Tag.Get("json"), ",")
			tag := tagSlice[0]
			input, ok := queries[tag]
			if !ok {
				continue
			}
			if err := convert(input, fVal); err != nil {
				logger.CtxLogErrorf(ctx, "parse url query error, tag:%s, error:%+v", tag, err)
				return err
			}
		}
	}

	return nil
}

// convert 将字符串列表转换为目标类型的值
func convert(sList []string, targetValue reflect.Value) error {
	if sList == nil || len(sList) == 0 {
		return nil
	}

	sLen := len(sList)

	var err error
	var iValue interface{}
	// 数值类型默认为10进制
	switch kind := targetValue.Kind(); kind {
	case reflect.Uint, reflect.Uint64, reflect.Uint32, reflect.Uint16, reflect.Uint8,
		reflect.Int, reflect.Int64, reflect.Int32, reflect.Int16, reflect.Int8,
		reflect.Float32, reflect.Float64, reflect.Bool, reflect.String:
		// use uint64 exchange uint
		iValue, err = ConvertBaseType(targetValue.Type(), sList[sLen-1])
		if err != nil {
			break
		}
		targetValue.Set(reflect.ValueOf(iValue).Convert(targetValue.Type()))

	case reflect.Slice:
		tempValue := reflect.MakeSlice(targetValue.Type(), sLen, sLen)
		for i, s := range sList {
			v := tempValue.Index(i)
			iValue, err = ConvertBaseType(tempValue.Type().Elem(), s)
			if err != nil {
				break
			}
			v.Set(reflect.ValueOf(iValue).Convert(tempValue.Type().Elem()))
		}
		targetValue.Set(tempValue)

	case reflect.Ptr:
		if targetValue.IsNil() {
			targetValue.Set(reflect.New(targetValue.Type().Elem()))
		}
		return convert(sList, targetValue.Elem())

	// other type will not support now, if you want to parse, please add you method
	default:
		err = fmt.Errorf("could not support convert string to this type %s", targetValue.Type().Name())
	}

	if err != nil {
		return err
	}

	return nil
}

// ConvertBaseType 转换基础类型
func ConvertBaseType(p reflect.Type, v interface{}) (interface{}, error) {
	if p.Kind() == reflect.Ptr {
		p = p.Elem()
	}
	switch p.Kind() {
	case reflect.Bool:
		return cast.ToBoolE(v)
	case reflect.String:
		s, err := cast.ToStringE(v)
		if err != nil {
			return nil, err
		}
		return s, nil
	case reflect.Int:
		return cast.ToIntE(v)
	case reflect.Int8:
		return cast.ToInt8E(v)
	case reflect.Int16:
		return cast.ToInt16E(v)
	case reflect.Int32:
		return cast.ToInt32E(v)
	case reflect.Int64:
		return cast.ToInt64E(v)
	case reflect.Uint:
		return cast.ToUintE(v)
	case reflect.Uint8:
		return cast.ToUint8E(v)
	case reflect.Uint16:
		return cast.ToUint16E(v)
	case reflect.Uint32:
		return cast.ToUint32E(v)
	case reflect.Uint64:
		return cast.ToUint64E(v)
	case reflect.Float32:
		return cast.ToFloat32E(v)
	case reflect.Float64:
		return cast.ToFloat64E(v)
	default:
		return v, nil
	}
}

// readFormOfPostBody 读取POST请求body中的form数据
func readFormOfPostBody(bs *restful.Context, schema interface{}) error {
	if getContentType(bs) != MIMEPOSTForm && getContentType(bs) != MIMEMultipartPOSTForm {
		return nil
	}

	//解析post请求body中的form参数
	req := bs.ReadRequest()
	_ = req.ParseMultipartForm(16 * (1 << 20))
	return mapForm(schema, req.Form, "form")
}

// getContentType 获取请求的Content-Type
func getContentType(bs *restful.Context) string {
	req := bs.ReadRequest()
	ct := req.Header.Get("Content-Type")
	if ct == "" {
		ct = MIMEJSON
	}
	mediaType, _, err := mime.ParseMediaType(ct)
	if err != nil {
		return MIMEJSON
	}
	return mediaType
}

// mapForm 将form数据映射到结构体
func mapForm(ptr interface{}, form map[string][]string, tag string) error {
	rt, rv := reflect.TypeOf(ptr), reflect.ValueOf(ptr)
	if rt.Kind() != reflect.Ptr || rt.Elem().Kind() != reflect.Struct {
		return nil
	}

	rt, rv = rt.Elem(), rv.Elem()
	for i := 0; i < rt.NumField(); i++ {
		typeField := rt.Field(i)
		structField := rv.Field(i)
		if !structField.CanSet() {
			continue
		}

		structFieldKind := structField.Kind()
		inputFieldName := typeField.Tag.Get(tag)
		if inputFieldName == "" {
			inputFieldName = typeField.Name

			// if "form" tag is nil, we inspect if the field is a struct.
			// this would not make sense for JSON parsing but it does for a form
			// since data is flatten
			if structFieldKind == reflect.Struct {
				err := mapForm(structField.Addr().Interface(), form, tag)
				if err != nil {
					return err
				}
				continue
			}
		}
		inputValue, exists := form[inputFieldName]
		if !exists {
			continue
		}

		numElems := len(inputValue)
		if structFieldKind == reflect.Slice && numElems > 0 {
			sliceOf := structField.Type().Elem()
			slice := reflect.MakeSlice(structField.Type(), numElems, numElems)
			for i := 0; i < numElems; i++ {
				if err := preSetWithProperType(sliceOf, inputValue[i], slice.Index(i)); err != nil {
					return err
				}
			}
			rv.Field(i).Set(slice)
		} else {
			if _, isTime := structField.Interface().(time.Time); isTime {
				if err := setTimeField(inputValue[0], typeField, structField); err != nil {
					return err
				}
				continue
			}
			if err := preSetWithProperType(typeField.Type, inputValue[0], structField); err != nil {
				return err
			}
		}
	}
	return nil
}

// preSetWithProperType 预设置字段值
func preSetWithProperType(rt reflect.Type, inputValue string, structField reflect.Value) error {
	if rt.Kind() == reflect.Ptr {
		newVal := reflect.New(rt.Elem())
		if err := setWithProperType(rt, inputValue, newVal); err != nil {
			return err
		}
		structField.Set(newVal)
	} else {
		if err := setWithProperType(rt, inputValue, structField); err != nil {
			return err
		}
	}
	return nil
}

// setWithProperType 根据类型设置字段值
func setWithProperType(valueKind reflect.Type, val string, structField reflect.Value) error {
	switch valueKind.Kind() {
	case reflect.Int:
		return setIntField(val, 0, structField)
	case reflect.Int8:
		return setIntField(val, 8, structField)
	case reflect.Int16:
		return setIntField(val, 16, structField)
	case reflect.Int32:
		return setIntField(val, 32, structField)
	case reflect.Int64:
		return setIntField(val, 64, structField)
	case reflect.Uint:
		return setUintField(val, 0, structField)
	case reflect.Uint8:
		return setUintField(val, 8, structField)
	case reflect.Uint16:
		return setUintField(val, 16, structField)
	case reflect.Uint32:
		return setUintField(val, 32, structField)
	case reflect.Uint64:
		return setUintField(val, 64, structField)
	case reflect.Bool:
		return setBoolField(val, structField)
	case reflect.Float32:
		return setFloatField(val, 32, structField)
	case reflect.Float64:
		return setFloatField(val, 64, structField)
	case reflect.String:
		structField.SetString(val)
	case reflect.Ptr:
		return setWithProperType(valueKind.Elem(), val, structField.Elem())

	default:
		return errors.New("Unknown type")
	}
	return nil
}

// setIntField 设置整型字段
func setIntField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0"
	}
	intVal, err := strconv.ParseInt(val, 10, bitSize)
	if err == nil {
		field.SetInt(intVal)
	}
	return err
}

// setUintField 设置无符号整型字段
func setUintField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0"
	}
	uintVal, err := strconv.ParseUint(val, 10, bitSize)
	if err == nil {
		field.SetUint(uintVal)
	}
	return err
}

// setBoolField 设置布尔型字段
func setBoolField(val string, field reflect.Value) error {
	if val == "" {
		val = "false"
	}
	boolVal, err := strconv.ParseBool(val)
	if err == nil {
		field.SetBool(boolVal)
	}
	return nil
}

// setFloatField 设置浮点型字段
func setFloatField(val string, bitSize int, field reflect.Value) error {
	if val == "" {
		val = "0.0"
	}
	floatVal, err := strconv.ParseFloat(val, bitSize)
	if err == nil {
		field.SetFloat(floatVal)
	}
	return err
}

// setTimeField 设置时间字段
func setTimeField(val string, structField reflect.StructField, value reflect.Value) error {
	timeFormat := structField.Tag.Get("time_format")
	if timeFormat == "" {
		return errors.New("blank time format")
	}

	if val == "" {
		value.Set(reflect.ValueOf(time.Time{}))
		return nil
	}

	l := time.Local
	if isUTC, _ := strconv.ParseBool(structField.Tag.Get("time_utc")); isUTC {
		l = time.UTC
	}

	if locTag := structField.Tag.Get("time_location"); locTag != "" {
		loc, err := time.LoadLocation(locTag)
		if err != nil {
			return err
		}
		l = loc
	}

	t, err := time.ParseInLocation(timeFormat, val, l)
	if err != nil {
		return err
	}

	value.Set(reflect.ValueOf(t))
	return nil
}
