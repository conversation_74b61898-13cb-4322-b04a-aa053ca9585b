package api

import (
	"context"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	fss_proto "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

const (
	ContentTypePB     string = "application/x-protobuf"
	ContentTypePBText string = "application/x-protobuf; charset=utf-8"
)

// PBRequest interface for Protocol Buffer message validation
// Note: PBSchemaParseValidate function has been merged into ProtoSchemaParseValidate in param_validate.go
type PBRequest interface {
	proto.Message
	Request
}

type Request interface {
	Validate() error
}

type PbResponse interface {
	proto.Message
	GetRespHeader() *fss_proto.RespHeader
}

func WritePBResp(ctx *restful.Context, resp PbResponse, err fsserr.Error) {
	respHeaderWithError(ctx.Ctx, resp.GetRespHeader(), err)
	if IsProtobufRequest(ctx) {
		writePBResp(ctx, resp)
		return
	}
	WriteJsonResp(ctx, resp, err)
}

// writePBResp writes Protocol Buffer response
func writePBResp(ctx *restful.Context, resp PbResponse) {
	// 存储响应到 context 中用于日志记录
	ctx.Ctx = ctxutils.SetPBResponse(ctx.Ctx, resp)

	// Marshal the protobuf message
	pbData, marshalErr := proto.Marshal(resp)
	if marshalErr != nil {
		// If marshaling fails, return error as JSON
		resp := WrapErrRsp(ctx.Ctx, fsserr.With(fsserr.ServerErr, marshalErr), nil)
		_ = ctx.WriteJSON(resp, ContentTypeJson)
		return
	}

	// Set the content type and write the response
	ctx.Response().Header().Set("Content-Type", ContentTypePBText)
	ctx.Response().WriteHeader(200)
	_, _ = ctx.Response().Write(pbData)
}

// GetContentType determines if the request is PB or JSON
func GetContentType(ctx *restful.Context) string {
	contentType := ctx.Request().HeaderParameter("Content-Type")
	if strings.Contains(contentType, "application/x-protobuf") {
		return ContentTypePB
	}
	return ApplicationJSON
}

// IsProtobufRequest checks if the request is a protobuf request
func IsProtobufRequest(ctx *restful.Context) bool {
	return GetContentType(ctx) == ContentTypePB
}

func respHeaderWithError(ctx context.Context, header *fss_proto.RespHeader, err fsserr.Error) {
	if header == nil || err == nil {
		return
	}
	header.RequestId = ctxutils.GetCtxData(ctx).GetRequestId()
	header.Code = int32(err.Code())
	header.Message = err.Message()
	header.Detail = err.Detail()
	if err.Reference().Source != "" {
		header.Reference = &fss_proto.Reference{
			Source:        string(err.Reference().Source),
			SourceCode:    err.Reference().SourceCode,
			SourceMessage: err.Reference().SourceMessage,
		}
	}
}

func NewRespHeader() *fss_proto.RespHeader {
	return &fss_proto.RespHeader{}
}
