package fsserr

import (
	_ "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service-protocol/protocol/go"
)

var (
	ServerErr                = newFssCode(11001, "server error") // fss_proto.ErrorCode_ERROR_SERVICE
	DataErr                  = newFssCode(11002, "data error")
	JsonErr                  = newFssCode(11003, "json error")
	NilErr                   = newFssCode(11004, "nil pointer error")
	TypeConvertErr           = newFssCode(11005, "type convert error")
	EmptyResultErr           = newFssCode(11006, "empty result")
	DegradeError             = newFssCode(11007, "service degrade") // fss_proto.ErrorCode_ERROR_DEGRADE
	RateLimitErr             = newFssCode(11008, "rate limit")      // fss_proto.ErrorCode_ERROR_RATE_LIMIT
	OperationLiveUnsupported = newFssCode(11009, "operation live unsupported")
	ConfigNotExisted         = newFssCode(11010, "config not existed")
	ForbiddenWriteErr        = newFssCode(11011, "forbidden write error")
	FileErr                  = newFssCode(11012, "file fail")
	KeyAlreadyExist          = newFssCode(11013, "key already exist")
)

var (
	DatabaseErr = newFssCode(12001, "db error")
	CodisErr    = newFssCode(12002, "codis error")
	FseErr      = newFssCode(12003, "fse error")
	SpexError   = newFssCode(12004, "spex api error")
	OmsError    = newFssCode(12005, "oms api error")
	SbsError    = newFssCode(12006, "sbs api error")
	LpsError    = newFssCode(12007, "lps api error")
)

var (
	LocalCacheErr = newFssCode(13001, "local cache error")
	LayerCacheErr = newFssCode(13002, "level cache error")
)

var (
	ParamErr                    = newFssCode(31001, "parameter validate fail")     // fss_proto.ErrorCode_ERROR_PARAMS
	BuyerAddressNotFoundErr     = newFssCode(31002, "buyer address not found")     // fss_proto.ErrorCode_ERROR_BUYER_ADDRESS_NOT_FOUND
	BuyerAddressInvalidErr      = newFssCode(31003, "buyer address invalid")       // fss_proto.ErrorCode_ERROR_BUYER_ADDRESS_INVALID
	SellerAddressNotFoundErr    = newFssCode(31004, "seller address not found")    // fss_proto.ErrorCode_ERROR_SELLER_ADDRESS_NOT_FOUND
	SellerAddressInvalidErr     = newFssCode(31005, "seller address invalid")      // fss_proto.ErrorCode_ERROR_SELLER_ADDRESS_INVALID
	WarehouseAddressNotFoundErr = newFssCode(31006, "warehouse address not found") // fss_proto.ErrorCode_ERROR_WAREHOUSE_ADDRESS_NOT_FOUND
	ItemNotFoundErr             = newFssCode(31007, "item not found")              // fss_proto.ErrorCode_ERROR_ITEM_NOT_FOUND
	ChannelNotFoundErr          = newFssCode(31008, "channel not found")           // fss_proto.ErrorCode_ERROR_CHANNEL_NOT_FOUND
)

var (
	WarehouseNotFoundErr      = newFssCode(32001, "warehouse not found")              // fss_proto.ErrorCode_ERROR_WAREHOUSE_NOT_FOUND
	ThreePFShopNoWarehouseErr = newFssCode(32002, "three physical shop no warehouse") // fss_proto.ErrorCode_ERROR_3PF_SHOP_NO_WAREHOUSE
	InvalidSocID              = newFssCode(32003, "invalid soc id")                   // fss_proto.ErrorCode_ERROR_INVALID_SOC_ID
)

var (
	OutOfStockErr                 = newFssCode(33001, "out of stock")         // fss_proto.ErrorCode_ERROR_OUT_OF_STOCK
	PackagePromotionOutOfStockErr = newFssCode(33002, "package out of stock") // fss_proto.ErrorCode_ERROR_PACKAGE_PROMOTION_OUT_OF_STOCK
	StockNotFound                 = newFssCode(33003, "stock not found")      // fss_proto.ErrorCode_ERROR_STOCK_NOT_FOUND
	InvalidItemStockErr           = newFssCode(33004, "invalid item stock")   // fss_proto.ErrorCode_ERROR_INVALID_ITEM_STOCK
)
