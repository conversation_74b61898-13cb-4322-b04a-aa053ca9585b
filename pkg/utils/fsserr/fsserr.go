package fsserr

import (
	"errors"
	"fmt"

	pkgErr "github.com/pkg/errors"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
)

type (
	FssError struct {
		codeMsg FssCode
		cause   error
		ref     Reference
	}

	Reference struct {
		Source        constant.SystemCode `json:"source"`
		SourceCode    string              `json:"source_code"`
		SourceMessage string              `json:"source_message"`
	}
)

func (p *FssError) Error() string {
	if !p.isFail() {
		return ""
	}
	return p.cause.Error()
}

func (p *FssError) Unwrap() error {
	if !p.isFail() {
		return nil
	}
	return p.cause
}

func (p *FssError) Code() int {
	if !p.isFail() {
		return 0
	}
	return p.codeMsg.Code()
}

func (p *FssError) Message() string {
	if !p.isFail() {
		return "success"
	}
	return p.codeMsg.Msg()
}

func (p *FssError) Detail() string {
	if !p.isFail() {
		return ""
	}
	if p.hasReference() {
		return fmt.Sprintf("Download err, system: %s, download code: %s, download message: %s, error: %s",
			p.Reference().SourceCode, p.Reference().SourceCode, p.Reference().SourceMessage, p.Error())
	}
	return p.Error()
}

func (p *FssError) Reference() Reference {
	if !p.isFail() {
		return Reference{}
	}
	return p.ref
}

func (p *FssError) WithReference(r Reference) Error {
	if !p.isFail() {
		return nil
	}
	p.ref = r
	return p
}

func (p *FssError) isFail() bool {
	return p != nil && p.cause != nil
}

func (p *FssError) hasReference() bool {
	return p != nil && p.ref != Reference{}
}

var (
	codeMap = make(map[int]FssCode)
)

type (
	FssCode struct {
		code int
		msg  string
	}
)

func newFssCode(code int, msg string) FssCode {
	oldCode, ok := codeMap[code]
	if ok {
		panic(fmt.Sprintf("error code define duplicated, code: %d, old: %s, new: %s",
			code, msg, oldCode.msg))
	}
	c := FssCode{
		msg:  msg,
		code: code,
	}
	codeMap[code] = c
	return c
}

func (c FssCode) Code() int {
	return c.code
}

func (c FssCode) Msg() string {
	return c.msg
}

// New 完全新建一个错误，用于偏底层操作错误
func New(cm FssCode, format string, a ...interface{}) *FssError {
	var err error
	if len(a) > 0 {
		err = pkgErr.Errorf(format, a...)
	} else {
		err = errors.New(format)
	}

	return &FssError{
		codeMsg: cm,
		cause:   err,
	}
}

// With 以当前错误为基础生成一个错误，用于偏底层操作错误
func With(cm FssCode, err error) *FssError {
	if err == nil {
		return nil
	}
	var fserr *FssError
	if errors.As(err, &fserr) && fserr != nil {
		return &FssError{
			codeMsg: cm,
			cause:   fserr.cause,
			ref:     fserr.ref,
		}
	}
	return &FssError{
		codeMsg: cm,
		cause:   err,
	}
}
