package fsserr

import (
	"errors"
	"fmt"

	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

func IsSpecialError[T error](err Error) (T, bool) {
	if err == nil {
		var t T
		return t, false
	}
	var t T
	ok := errors.As(err, &t)
	return t, ok
}

type OutOfStockError struct {
	IsPackagePromotion bool
	Message            string
	Err                error
	ItemId             []typ.ItemIdType
	ItemStockInfos     []ItemStockInfo
}

type ItemStockInfo struct {
	ItemId         typ.ItemIdType
	ModelId        typ.ModelIdType
	ItemGroupId    uint64
	AvailableStock uint32
	ShopId         uint64
}

func (e *OutOfStockError) Error() string {
	itemStr, _ := jsoniter.Marshal(e.ItemId)
	if e.Err == nil {
		return fmt.Sprintf(
			"isPackagePromotion=%t,msg:%s, items:%s",
			e.IsPackagePromotion,
			e.Message,
			string(itemStr),
		)
	} else {
		return fmt.Sprintf(
			"isPackagePromotion=%t,msg:%s,defail:%s, items:%s",
			e.IsPackagePromotion,
			e.Message,
			e.Err.Error(),
			string(itemStr),
		)
	}
}

func (e *OutOfStockError) Unwrap() error {
	return e.Err
}

type IgsSystemError struct {
	Message string
	Err     error
}

func (e *IgsSystemError) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("%s", e.Message)
	} else {
		return fmt.Sprintf("%s:%s", e.Message, e.Err.Error())
	}
}

func (e *IgsSystemError) Unwrap() error {
	return e.Err
}

type NoAddressError struct {
	Message string
	Err     error
}

func (e *NoAddressError) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("%s", e.Message)
	} else {
		return fmt.Sprintf("%s:%s", e.Message, e.Err.Error())
	}
}

func (e *NoAddressError) Unwrap() error {
	return e.Err
}

type InvalidRequest struct {
	Message string
	Err     error
}

func (e *InvalidRequest) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("invalid request, %s", e.Message)
	} else {
		return fmt.Sprintf("invalid request, %s:%s", e.Message, e.Err.Error())
	}
}

func (e *InvalidRequest) Unwrap() error {
	return e.Err
}

type StockNotFoundError struct {
	Message string
	Err     error
}

func (e *StockNotFoundError) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("%s", e.Message)
	} else {
		return fmt.Sprintf("%s:%s", e.Message, e.Err.Error())
	}
}

func (e *StockNotFoundError) Unwrap() error {
	return e.Err
}

type InvalidItemStock struct {
	Message string
	Err     error
}

func (e *InvalidItemStock) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("%s", e.Message)
	} else {
		return fmt.Sprintf("%s:%s", e.Message, e.Err.Error())
	}
}

func (e *InvalidItemStock) Unwrap() error {
	return e.Err
}

type SourceSplitFailed struct {
	Message string
	// strategies.SourceSplitStrategy
	Strategy interface{}
	Err      error
}

func (e *SourceSplitFailed) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("%s:%#v", e.Message, e.Strategy)
	} else {
		return fmt.Sprintf("%s:%#v:%s", e.Message, e.Strategy, e.Err.Error())
	}
}

func (e *SourceSplitFailed) Unwrap() error {
	return e.Err
}

type UnexpectedBizScenarioError struct {
	message string
	err     error
}

func NewUnexpectedBizScenarioError(message string) UnexpectedBizScenarioError {
	return UnexpectedBizScenarioError{message: message}
}

func (e UnexpectedBizScenarioError) Error() string {
	if e.err == nil {
		return fmt.Sprintf("%s", e.message)
	} else {
		return fmt.Sprintf("%s:%s", e.message, e.err.Error())
	}
}

func (e UnexpectedBizScenarioError) Unwrap() error {
	return e.err
}

var Err3PFShopNoWH = NewUnexpectedBizScenarioError("3pf shop has no warehouse")
var Err3PFHybridShopTooManyLocalWHs = NewUnexpectedBizScenarioError("3pf hybrid shop has more than 1 local warehouses, cannot split")
var ErrSellerWHAddressNotFound = NewUnexpectedBizScenarioError("seller WH address_id not found from dependency data, ref: https://jira.shopee.io/browse/SPOT-52557")
var ErrWeightAndDimensionLimit = NewWeightAndDimensionLimitError()

func BuildOOSError(isPackagePromotion bool, itemId []typ.ItemIdType) *OutOfStockError {
	return &OutOfStockError{
		IsPackagePromotion: isPackagePromotion,
		Message:            "out of stock",
		ItemId:             itemId,
	}
}

func BuildInvalidReqError(message string) *InvalidRequest {
	return &InvalidRequest{
		Message: message,
	}
}

type InvalidSOCIDReqError struct {
	Message string
	Err     error
}

func BuildInvalidSOCIDReqError(message string) *InvalidSOCIDReqError {
	return &InvalidSOCIDReqError{
		Message: message,
	}
}

func (e *InvalidSOCIDReqError) Error() string {
	if e.Err == nil {
		return fmt.Sprintf("%s", e.Message)
	} else {
		return fmt.Sprintf("%s:%s", e.Message, e.Err.Error())
	}
}

type WeightAndDimensionLimitError struct{}

func NewWeightAndDimensionLimitError() *WeightAndDimensionLimitError {
	return &WeightAndDimensionLimitError{}
}

func (e *WeightAndDimensionLimitError) Error() string {
	return "One item exceeds weight or height limitation"
}
