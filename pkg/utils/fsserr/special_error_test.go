package fsserr

import (
	"errors"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

func TestIsSpecialErrorWithFssError(t *testing.T) {
	// Test IsSpecialError function with FssError wrapping special error types
	tests := []struct {
		name     string
		err      Error
		wantType interface{}
		wantOk   bool
	}{
		{
			name:     "nil error",
			err:      nil,
			wantType: (*OutOfStockError)(nil),
			wantOk:   false,
		},
		{
			name:     "FssError wrapping OutOfStockError",
			err:      With(OutOfStockErr, &OutOfStockError{Message: "test"}),
			wantType: &OutOfStockError{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping IgsSystemError",
			err:      With(ServerErr, &IgsSystemError{Message: "test"}),
			wantType: &IgsSystemError{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping NoAddressError",
			err:      With(DataErr, &NoAddressError{Message: "test"}),
			wantType: &NoAddressError{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping InvalidRequest",
			err:      With(ParamErr, &InvalidRequest{Message: "test"}),
			wantType: &InvalidRequest{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping StockNotFoundError",
			err:      With(EmptyResultErr, &StockNotFoundError{Message: "test"}),
			wantType: &StockNotFoundError{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping InvalidItemStock",
			err:      With(DataErr, &InvalidItemStock{Message: "test"}),
			wantType: &InvalidItemStock{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping SourceSplitFailed",
			err:      With(ServerErr, &SourceSplitFailed{Message: "test"}),
			wantType: &SourceSplitFailed{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping InvalidSOCIDReqError",
			err:      With(ParamErr, &InvalidSOCIDReqError{Message: "test"}),
			wantType: &InvalidSOCIDReqError{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping WeightAndDimensionLimitError",
			err:      With(DataErr, &WeightAndDimensionLimitError{}),
			wantType: &WeightAndDimensionLimitError{},
			wantOk:   true,
		},
		{
			name:     "FssError wrapping UnexpectedBizScenarioError",
			err:      With(ServerErr, UnexpectedBizScenarioError{message: "test"}),
			wantType: UnexpectedBizScenarioError{},
			wantOk:   true,
		},
		{
			name:     "FssError with regular error",
			err:      With(ServerErr, errors.New("regular error")),
			wantType: &OutOfStockError{},
			wantOk:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			switch tt.wantType.(type) {
			case *OutOfStockError:
				got, ok := IsSpecialError[*OutOfStockError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *IgsSystemError:
				got, ok := IsSpecialError[*IgsSystemError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *NoAddressError:
				got, ok := IsSpecialError[*NoAddressError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *InvalidRequest:
				got, ok := IsSpecialError[*InvalidRequest](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *StockNotFoundError:
				got, ok := IsSpecialError[*StockNotFoundError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *InvalidItemStock:
				got, ok := IsSpecialError[*InvalidItemStock](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *SourceSplitFailed:
				got, ok := IsSpecialError[*SourceSplitFailed](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *InvalidSOCIDReqError:
				got, ok := IsSpecialError[*InvalidSOCIDReqError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case *WeightAndDimensionLimitError:
				got, ok := IsSpecialError[*WeightAndDimensionLimitError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got == nil {
					t.Errorf("IsSpecialError() got nil, want non-nil")
				}
			case UnexpectedBizScenarioError:
				got, ok := IsSpecialError[UnexpectedBizScenarioError](tt.err)
				if ok != tt.wantOk {
					t.Errorf("IsSpecialError() ok = %v, want %v", ok, tt.wantOk)
				}
				if tt.wantOk && got.message == "" {
					t.Errorf("IsSpecialError() got empty message, want non-empty")
				}
			}
		})
	}
}

func TestFssErrorWithSpecialErrors(t *testing.T) {
	// Test FssError wrapping various special error types
	tests := []struct {
		name        string
		specialErr  error
		fssCode     FssCode
		wantCode    int
		wantMessage string
	}{
		{
			name:        "OutOfStockError with OutOfStockErr",
			specialErr:  &OutOfStockError{Message: "out of stock", ItemId: []typ.ItemIdType{123}},
			fssCode:     OutOfStockErr,
			wantCode:    32001,
			wantMessage: "out of stock",
		},
		{
			name:        "IgsSystemError with ServerErr",
			specialErr:  &IgsSystemError{Message: "system error"},
			fssCode:     ServerErr,
			wantCode:    31001,
			wantMessage: "server error",
		},
		{
			name:        "NoAddressError with DataErr",
			specialErr:  &NoAddressError{Message: "no address"},
			fssCode:     DataErr,
			wantCode:    31002,
			wantMessage: "data error",
		},
		{
			name:        "InvalidRequest with ParamErr",
			specialErr:  &InvalidRequest{Message: "invalid request"},
			fssCode:     ParamErr,
			wantCode:    100001,
			wantMessage: "parameter validate fail",
		},
		{
			name:        "StockNotFoundError with EmptyResultErr",
			specialErr:  &StockNotFoundError{Message: "stock not found"},
			fssCode:     EmptyResultErr,
			wantCode:    31007,
			wantMessage: "empty result",
		},
		{
			name:        "InvalidItemStock with DataErr",
			specialErr:  &InvalidItemStock{Message: "invalid stock"},
			fssCode:     DataErr,
			wantCode:    31002,
			wantMessage: "data error",
		},
		{
			name:        "SourceSplitFailed with ServerErr",
			specialErr:  &SourceSplitFailed{Message: "split failed"},
			fssCode:     ServerErr,
			wantCode:    31001,
			wantMessage: "server error",
		},
		{
			name:        "InvalidSOCIDReqError with ParamErr",
			specialErr:  &InvalidSOCIDReqError{Message: "invalid SOC ID"},
			fssCode:     ParamErr,
			wantCode:    100001,
			wantMessage: "parameter validate fail",
		},
		{
			name:        "WeightAndDimensionLimitError with DataErr",
			specialErr:  &WeightAndDimensionLimitError{},
			fssCode:     DataErr,
			wantCode:    31002,
			wantMessage: "data error",
		},
		{
			name:        "UnexpectedBizScenarioError with ServerErr",
			specialErr:  UnexpectedBizScenarioError{message: "unexpected scenario"},
			fssCode:     ServerErr,
			wantCode:    31001,
			wantMessage: "server error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fssErr := With(tt.fssCode, tt.specialErr)

			// Test FssError properties
			if fssErr == nil {
				t.Fatal("FssError should not be nil")
			}

			if fssErr.Code() != tt.wantCode {
				t.Errorf("FssError.Code() = %v, want %v", fssErr.Code(), tt.fssCode.Code())
			}

			if fssErr.Message() != tt.wantMessage {
				t.Errorf("FssError.Message() = %v, want %v", fssErr.Message(), tt.wantMessage)
			}

			// Test that the original special error is preserved
			if fssErr.Error() != tt.specialErr.Error() {
				t.Errorf("FssError.Error() = %v, want %v", fssErr.Error(), tt.specialErr.Error())
			}

			// Test Unwrap returns the original special error
			unwrapped := fssErr.Unwrap()
			if unwrapped != tt.specialErr {
				t.Errorf("FssError.Unwrap() = %v, want %v", unwrapped, tt.specialErr)
			}
		})
	}
}

func TestFssErrorWithWrappedSpecialErrors(t *testing.T) {
	// Test FssError wrapping special errors that have wrapped errors
	tests := []struct {
		name       string
		specialErr error
		fssCode    FssCode
	}{
		{
			name: "OutOfStockError with wrapped error",
			specialErr: &OutOfStockError{
				Message: "out of stock",
				Err:     errors.New("database error"),
				ItemId:  []typ.ItemIdType{123},
			},
			fssCode: OutOfStockErr,
		},
		{
			name: "IgsSystemError with wrapped error",
			specialErr: &IgsSystemError{
				Message: "system error",
				Err:     errors.New("network error"),
			},
			fssCode: ServerErr,
		},
		{
			name: "SourceSplitFailed with wrapped error",
			specialErr: &SourceSplitFailed{
				Message:  "split failed",
				Strategy: "test strategy",
				Err:      errors.New("internal error"),
			},
			fssCode: ServerErr,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fssErr := With(tt.fssCode, tt.specialErr)

			if fssErr == nil {
				t.Fatal("FssError should not be nil")
			}

			// Test that the error message includes the wrapped error
			errorMsg := fssErr.Error()
			if errorMsg != tt.specialErr.Error() {
				t.Errorf("FssError.Error() = %v, want %v", errorMsg, tt.specialErr.Error())
			}

			// Test Unwrap returns the original special error
			unwrapped := fssErr.Unwrap()
			if unwrapped != tt.specialErr {
				t.Errorf("FssError.Unwrap() = %v, want %v", unwrapped, tt.specialErr)
			}
		})
	}
}

func TestFssErrorWithNilError(t *testing.T) {
	// Test With method with nil error
	fssErr := With(OutOfStockErr, nil)
	if fssErr != nil {
		t.Errorf("With(nil) should return nil, got %v", fssErr)
	}
}

func TestFssErrorWithReference(t *testing.T) {
	// Test FssError with reference information
	specialErr := &OutOfStockError{
		Message: "out of stock",
		ItemId:  []typ.ItemIdType{123},
	}

	fssErr := With(OutOfStockErr, specialErr)
	if fssErr == nil {
		t.Fatal("FssError should not be nil")
	}

	// Add reference
	ref := Reference{
		Source:        "TEST",
		SourceCode:    "TEST001",
		SourceMessage: "Test error",
	}

	fssErrWithRef := fssErr.WithReference(ref)
	if fssErrWithRef == nil {
		t.Fatal("FssError with reference should not be nil")
	}

	// Test reference is set
	gotRef := fssErrWithRef.Reference()
	if gotRef != ref {
		t.Errorf("Reference() = %v, want %v", gotRef, ref)
	}

	// Test Detail includes reference information
	detail := fssErrWithRef.Detail()
	if detail == "" {
		t.Error("Detail() should not be empty when reference is set")
	}
}

func TestOutOfStockError_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *OutOfStockError
		want string
	}{
		{
			name: "without wrapped error",
			err: &OutOfStockError{
				IsPackagePromotion: true,
				Message:            "test message",
				ItemId:             []typ.ItemIdType{123, 456},
			},
			want: "isPackagePromotion=true,msg:test message, items:[123,456]",
		},
		{
			name: "with wrapped error",
			err: &OutOfStockError{
				IsPackagePromotion: false,
				Message:            "test message",
				Err:                errors.New("wrapped error"),
				ItemId:             []typ.ItemIdType{789},
			},
			want: "isPackagePromotion=false,msg:test message,defail:wrapped error, items:[789]",
		},
		{
			name: "empty item list",
			err: &OutOfStockError{
				IsPackagePromotion: true,
				Message:            "empty items",
				ItemId:             []typ.ItemIdType{},
			},
			want: "isPackagePromotion=true,msg:empty items, items:[]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("OutOfStockError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOutOfStockError_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &OutOfStockError{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("OutOfStockError.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestIgsSystemError_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *IgsSystemError
		want string
	}{
		{
			name: "without wrapped error",
			err: &IgsSystemError{
				Message: "test message",
			},
			want: "test message",
		},
		{
			name: "with wrapped error",
			err: &IgsSystemError{
				Message: "test message",
				Err:     errors.New("wrapped error"),
			},
			want: "test message:wrapped error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("IgsSystemError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIgsSystemError_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &IgsSystemError{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("IgsSystemError.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestNoAddressError_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *NoAddressError
		want string
	}{
		{
			name: "without wrapped error",
			err: &NoAddressError{
				Message: "no address found",
			},
			want: "no address found",
		},
		{
			name: "with wrapped error",
			err: &NoAddressError{
				Message: "no address found",
				Err:     errors.New("database error"),
			},
			want: "no address found:database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("NoAddressError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNoAddressError_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &NoAddressError{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("NoAddressError.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestInvalidRequest_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *InvalidRequest
		want string
	}{
		{
			name: "without wrapped error",
			err: &InvalidRequest{
				Message: "invalid parameters",
			},
			want: "invalid request, invalid parameters",
		},
		{
			name: "with wrapped error",
			err: &InvalidRequest{
				Message: "invalid parameters",
				Err:     errors.New("validation error"),
			},
			want: "invalid request, invalid parameters:validation error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("InvalidRequest.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInvalidRequest_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &InvalidRequest{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("InvalidRequest.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestStockNotFoundError_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *StockNotFoundError
		want string
	}{
		{
			name: "without wrapped error",
			err: &StockNotFoundError{
				Message: "stock not found",
			},
			want: "stock not found",
		},
		{
			name: "with wrapped error",
			err: &StockNotFoundError{
				Message: "stock not found",
				Err:     errors.New("database error"),
			},
			want: "stock not found:database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("StockNotFoundError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStockNotFoundError_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &StockNotFoundError{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("StockNotFoundError.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestInvalidItemStock_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *InvalidItemStock
		want string
	}{
		{
			name: "without wrapped error",
			err: &InvalidItemStock{
				Message: "invalid stock",
			},
			want: "invalid stock",
		},
		{
			name: "with wrapped error",
			err: &InvalidItemStock{
				Message: "invalid stock",
				Err:     errors.New("validation error"),
			},
			want: "invalid stock:validation error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("InvalidItemStock.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInvalidItemStock_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &InvalidItemStock{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("InvalidItemStock.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestSourceSplitFailed_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *SourceSplitFailed
		want string
	}{
		{
			name: "without wrapped error",
			err: &SourceSplitFailed{
				Message:  "split failed",
				Strategy: "test strategy",
			},
			want: "split failed:\"test strategy\"",
		},
		{
			name: "with wrapped error",
			err: &SourceSplitFailed{
				Message:  "split failed",
				Strategy: "test strategy",
				Err:      errors.New("internal error"),
			},
			want: "split failed:\"test strategy\":internal error",
		},
		{
			name: "with nil strategy",
			err: &SourceSplitFailed{
				Message:  "split failed",
				Strategy: nil,
			},
			want: "split failed:<nil>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("SourceSplitFailed.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSourceSplitFailed_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := &SourceSplitFailed{
		Message: "test",
		Err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("SourceSplitFailed.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestUnexpectedBizScenarioError_Error(t *testing.T) {
	tests := []struct {
		name string
		err  UnexpectedBizScenarioError
		want string
	}{
		{
			name: "without wrapped error",
			err: UnexpectedBizScenarioError{
				message: "unexpected scenario",
			},
			want: "unexpected scenario",
		},
		{
			name: "with wrapped error",
			err: UnexpectedBizScenarioError{
				message: "unexpected scenario",
				err:     errors.New("internal error"),
			},
			want: "unexpected scenario:internal error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("UnexpectedBizScenarioError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestUnexpectedBizScenarioError_Unwrap(t *testing.T) {
	wrappedErr := errors.New("wrapped error")
	err := UnexpectedBizScenarioError{
		message: "test",
		err:     wrappedErr,
	}

	got := err.Unwrap()
	if got != wrappedErr {
		t.Errorf("UnexpectedBizScenarioError.Unwrap() = %v, want %v", got, wrappedErr)
	}
}

func TestNewUnexpectedBizScenarioError(t *testing.T) {
	message := "test message"
	err := NewUnexpectedBizScenarioError(message)

	if err.message != message {
		t.Errorf("NewUnexpectedBizScenarioError() message = %v, want %v", err.message, message)
	}

	if err.err != nil {
		t.Errorf("NewUnexpectedBizScenarioError() err = %v, want nil", err.err)
	}
}

func TestInvalidSOCIDReqError_Error(t *testing.T) {
	tests := []struct {
		name string
		err  *InvalidSOCIDReqError
		want string
	}{
		{
			name: "without wrapped error",
			err: &InvalidSOCIDReqError{
				Message: "invalid SOC ID",
			},
			want: "invalid SOC ID",
		},
		{
			name: "with wrapped error",
			err: &InvalidSOCIDReqError{
				Message: "invalid SOC ID",
				Err:     errors.New("validation error"),
			},
			want: "invalid SOC ID:validation error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.err.Error()
			if got != tt.want {
				t.Errorf("InvalidSOCIDReqError.Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWeightAndDimensionLimitError_Error(t *testing.T) {
	err := &WeightAndDimensionLimitError{}
	got := err.Error()
	want := "One item exceeds weight or height limitation"

	if got != want {
		t.Errorf("WeightAndDimensionLimitError.Error() = %v, want %v", got, want)
	}
}

func TestNewWeightAndDimensionLimitError(t *testing.T) {
	var err error = NewWeightAndDimensionLimitError()

	if err == nil {
		t.Error("NewWeightAndDimensionLimitError() returned nil")
	}

	// Test that it's the correct type
	_, ok := err.(*WeightAndDimensionLimitError)
	if !ok {
		t.Error("NewWeightAndDimensionLimitError() returned wrong type")
	}
}

func TestBuildOOSError(t *testing.T) {
	tests := []struct {
		name                   string
		isPackagePromotion     bool
		itemId                 []typ.ItemIdType
		wantIsPackagePromotion bool
		wantMessage            string
		wantItemId             []typ.ItemIdType
	}{
		{
			name:                   "package promotion with items",
			isPackagePromotion:     true,
			itemId:                 []typ.ItemIdType{123, 456},
			wantIsPackagePromotion: true,
			wantMessage:            "out of stock",
			wantItemId:             []typ.ItemIdType{123, 456},
		},
		{
			name:                   "non-package promotion with empty items",
			isPackagePromotion:     false,
			itemId:                 []typ.ItemIdType{},
			wantIsPackagePromotion: false,
			wantMessage:            "out of stock",
			wantItemId:             []typ.ItemIdType{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := BuildOOSError(tt.isPackagePromotion, tt.itemId)

			if got.IsPackagePromotion != tt.wantIsPackagePromotion {
				t.Errorf("BuildOOSError() IsPackagePromotion = %v, want %v", got.IsPackagePromotion, tt.wantIsPackagePromotion)
			}

			if got.Message != tt.wantMessage {
				t.Errorf("BuildOOSError() Message = %v, want %v", got.Message, tt.wantMessage)
			}

			if len(got.ItemId) != len(tt.wantItemId) {
				t.Errorf("BuildOOSError() ItemId length = %v, want %v", len(got.ItemId), len(tt.wantItemId))
			}

			for i, id := range got.ItemId {
				if id != tt.wantItemId[i] {
					t.Errorf("BuildOOSError() ItemId[%d] = %v, want %v", i, id, tt.wantItemId[i])
				}
			}
		})
	}
}

func TestBuildInvalidReqError(t *testing.T) {
	message := "test invalid request"
	err := BuildInvalidReqError(message)

	if err.Message != message {
		t.Errorf("BuildInvalidReqError() Message = %v, want %v", err.Message, message)
	}

	if err.Err != nil {
		t.Errorf("BuildInvalidReqError() Err = %v, want nil", err.Err)
	}
}

func TestBuildInvalidSOCIDReqError(t *testing.T) {
	message := "test invalid SOC ID request"
	err := BuildInvalidSOCIDReqError(message)

	if err.Message != message {
		t.Errorf("BuildInvalidSOCIDReqError() Message = %v, want %v", err.Message, message)
	}

	if err.Err != nil {
		t.Errorf("BuildInvalidSOCIDReqError() Err = %v, want nil", err.Err)
	}
}

func TestPredefinedErrors(t *testing.T) {
	// Test predefined error constants
	if Err3PFShopNoWH.Error() != "3pf shop has no warehouse" {
		t.Errorf("Err3PFShopNoWH.Error() = %v, want '3pf shop has no warehouse'", Err3PFShopNoWH.Error())
	}

	if Err3PFHybridShopTooManyLocalWHs.Error() != "3pf hybrid shop has more than 1 local warehouses, cannot split" {
		t.Errorf("Err3PFHybridShopTooManyLocalWHs.Error() = %v, want '3pf hybrid shop has more than 1 local warehouses, cannot split'", Err3PFHybridShopTooManyLocalWHs.Error())
	}

	if ErrSellerWHAddressNotFound.Error() != "seller WH address_id not found from dependency data, ref: https://jira.shopee.io/browse/SPOT-52557" {
		t.Errorf("ErrSellerWHAddressNotFound.Error() = %v, want 'seller WH address_id not found from dependency data, ref: https://jira.shopee.io/browse/SPOT-52557'", ErrSellerWHAddressNotFound.Error())
	}

	if ErrWeightAndDimensionLimit.Error() != "One item exceeds weight or height limitation" {
		t.Errorf("ErrWeightAndDimensionLimit.Error() = %v, want 'One item exceeds weight or height limitation'", ErrWeightAndDimensionLimit.Error())
	}
}

func TestItemStockInfo(t *testing.T) {
	// Test ItemStockInfo struct (basic functionality)
	itemStockInfo := ItemStockInfo{
		ItemId:         123,
		ModelId:        456,
		ItemGroupId:    789,
		AvailableStock: 10,
		ShopId:         999,
	}

	if itemStockInfo.ItemId != 123 {
		t.Errorf("ItemStockInfo.ItemId = %v, want 123", itemStockInfo.ItemId)
	}

	if itemStockInfo.ModelId != 456 {
		t.Errorf("ItemStockInfo.ModelId = %v, want 456", itemStockInfo.ModelId)
	}

	if itemStockInfo.ItemGroupId != 789 {
		t.Errorf("ItemStockInfo.ItemGroupId = %v, want 789", itemStockInfo.ItemGroupId)
	}

	if itemStockInfo.AvailableStock != 10 {
		t.Errorf("ItemStockInfo.AvailableStock = %v, want 10", itemStockInfo.AvailableStock)
	}

	if itemStockInfo.ShopId != 999 {
		t.Errorf("ItemStockInfo.ShopId = %v, want 999", itemStockInfo.ShopId)
	}
}
